// Professional Semantic Search Service

import { embeddingService } from './embedding-service'
import { CacheService } from './cache-service'
import { SEARCH_CONFIG, CACHE_CONFIG } from '@/constants'
import type { 
  EmbeddingResult, 
  SearchResult, 
  SearchQuery,
  SimilarityR<PERSON>ult 
} from '@/types'

export class SearchService {
  private corpus: EmbeddingResult[] = []
  private labels: string[] = []
  private ids: string[] = []
  private searchHistory: SearchQuery[] = []
  private cache: CacheService<SearchQuery>

  constructor() {
    this.cache = new CacheService<SearchQuery>(
      CACHE_CONFIG.search.key,
      CACHE_CONFIG.search.maxSize,
      CACHE_CONFIG.search.ttl
    )
  }

  /**
   * Update the search corpus
   */
  updateCorpus(embeddings: EmbeddingResult[], labels: string[], ids: string[]): void {
    if (embeddings.length !== labels.length || embeddings.length !== ids.length) {
      throw new Error('Embeddings, labels, and IDs arrays must have the same length')
    }

    this.corpus = [...embeddings]
    this.labels = [...labels]
    this.ids = [...ids]
    
    // Clear cache when corpus changes
    this.cache.clear()
  }

  /**
   * Add documents to the search corpus
   */
  async addDocuments(documents: Array<{ id: string; text: string; label: string }>): Promise<void> {
    for (const doc of documents) {
      const embedding = await embeddingService.generateEmbedding(doc.text)
      this.corpus.push(embedding)
      this.labels.push(doc.label)
      this.ids.push(doc.id)
    }
  }

  /**
   * Perform semantic search
   */
  async search(
    query: string,
    options: {
      topK?: number
      threshold?: number
      includeHighlights?: boolean
      useCache?: boolean
    } = {}
  ): Promise<SearchQuery> {
    const startTime = Date.now()
    const {
      topK = SEARCH_CONFIG.defaultTopK,
      threshold = SEARCH_CONFIG.minSimilarity,
      includeHighlights = true,
      useCache = true,
    } = options

    // Check cache first
    const cacheKey = this.getCacheKey(query, options)
    if (useCache) {
      const cached = this.cache.get(cacheKey)
      if (cached) {
        return cached
      }
    }

    if (this.corpus.length === 0) {
      const emptyResult: SearchQuery = {
        id: this.generateId(),
        query,
        timestamp: new Date(),
        results: [],
        processingTime: Date.now() - startTime,
      }
      
      this.searchHistory.push(emptyResult)
      return emptyResult
    }

    try {
      // Generate embedding for the query
      const queryEmbedding = await embeddingService.generateEmbedding(query)

      // Find most similar documents
      const similarResults = embeddingService.findMostSimilar(
        queryEmbedding,
        this.corpus,
        Math.min(topK, this.corpus.length)
      )

      // Filter by threshold and create search results
      const results: SearchResult[] = similarResults
        .filter(result => result.similarity >= threshold)
        .map((result, index) => {
          const corpusIndex = this.corpus.indexOf(result.result)

          return {
            id: this.ids[corpusIndex],
            text: result.result.text,
            label: this.labels[corpusIndex],
            similarity: result.similarity,
            embedding: result.result,
            rank: index + 1,
            highlights: includeHighlights 
              ? this.extractHighlights(query, result.result.text)
              : undefined,
          }
        })

      const searchQuery: SearchQuery = {
        id: this.generateId(),
        query,
        timestamp: new Date(),
        results,
        processingTime: Date.now() - startTime,
      }

      // Cache the result
      if (useCache) {
        this.cache.set(cacheKey, searchQuery)
      }

      // Add to search history
      this.searchHistory.push(searchQuery)
      
      // Keep history size manageable
      if (this.searchHistory.length > 100) {
        this.searchHistory = this.searchHistory.slice(-50)
      }

      return searchQuery
    } catch (error) {
      throw new Error(`Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get search suggestions based on query
   */
  async getSuggestions(
    partialQuery: string,
    maxSuggestions = 5
  ): Promise<string[]> {
    if (partialQuery.length < 2) return []

    const suggestions = new Set<string>()
    
    // Add suggestions from search history
    this.searchHistory
      .filter(search => 
        search.query.toLowerCase().includes(partialQuery.toLowerCase())
      )
      .slice(-10)
      .forEach(search => suggestions.add(search.query))

    // Add suggestions from corpus text
    this.corpus
      .filter(embedding => 
        embedding.text.toLowerCase().includes(partialQuery.toLowerCase())
      )
      .slice(0, maxSuggestions)
      .forEach(embedding => {
        // Extract relevant phrases
        const words = embedding.text.split(/\s+/)
        const queryWords = partialQuery.toLowerCase().split(/\s+/)
        
        for (let i = 0; i < words.length - queryWords.length + 1; i++) {
          const phrase = words.slice(i, i + queryWords.length + 2).join(' ')
          if (phrase.toLowerCase().includes(partialQuery.toLowerCase())) {
            suggestions.add(phrase)
          }
        }
      })

    return Array.from(suggestions).slice(0, maxSuggestions)
  }

  /**
   * Find similar documents to a given document
   */
  findSimilarDocuments(
    documentId: string,
    topK = 5,
    threshold = 0.5
  ): SearchResult[] {
    const documentIndex = this.ids.indexOf(documentId)
    if (documentIndex === -1) {
      throw new Error('Document not found in corpus')
    }

    const queryEmbedding = this.corpus[documentIndex]
    const similarResults = embeddingService.findMostSimilar(
      queryEmbedding,
      this.corpus.filter((_, index) => index !== documentIndex),
      topK
    )

    return similarResults
      .filter(result => result.similarity >= threshold)
      .map((result, index) => {
        const corpusIndex = this.corpus.indexOf(result.result)
        
        return {
          id: this.ids[corpusIndex],
          text: result.result.text,
          label: this.labels[corpusIndex],
          similarity: result.similarity,
          embedding: result.result,
          rank: index + 1,
        }
      })
  }

  /**
   * Get search history
   */
  getSearchHistory(limit = 10): SearchQuery[] {
    return this.searchHistory
      .slice(-limit)
      .reverse()
  }

  /**
   * Clear search history
   */
  clearSearchHistory(): void {
    this.searchHistory = []
    this.cache.clear()
  }

  /**
   * Get search statistics
   */
  getSearchStats() {
    const totalSearches = this.searchHistory.length
    const avgResultsPerSearch = totalSearches > 0
      ? this.searchHistory.reduce((sum, search) => sum + search.results.length, 0) / totalSearches
      : 0
    
    const avgProcessingTime = totalSearches > 0
      ? this.searchHistory.reduce((sum, search) => sum + search.processingTime, 0) / totalSearches
      : 0

    const topQueries = this.getTopQueries(5)
    
    return {
      totalSearches,
      avgResultsPerSearch,
      avgProcessingTime,
      topQueries,
      corpusSize: this.corpus.length,
      cacheStats: this.cache.getStats(),
    }
  }

  /**
   * Export search data
   */
  exportSearchData() {
    return {
      corpus: this.corpus.map((embedding, index) => ({
        id: this.ids[index],
        text: embedding.text,
        label: this.labels[index],
        dimension: embedding.dimension,
        model: embedding.model,
      })),
      searchHistory: this.searchHistory,
      stats: this.getSearchStats(),
      timestamp: new Date().toISOString(),
    }
  }

  private extractHighlights(query: string, text: string): string[] {
    const queryWords = query.toLowerCase().split(/\s+/)
    const textWords = text.split(/\s+/)
    const highlights: string[] = []

    // Find sentences containing query words
    const sentences = text.split(/[.!?]+/)
    
    for (const sentence of sentences) {
      const sentenceLower = sentence.toLowerCase()
      const matchCount = queryWords.filter(word => 
        sentenceLower.includes(word)
      ).length
      
      if (matchCount > 0) {
        highlights.push(sentence.trim())
      }
    }

    // If no sentence matches, find phrases with query words
    if (highlights.length === 0) {
      for (let i = 0; i < textWords.length - 5; i++) {
        const phrase = textWords.slice(i, i + 6).join(' ')
        const phraseLower = phrase.toLowerCase()
        
        if (queryWords.some(word => phraseLower.includes(word))) {
          highlights.push(phrase)
        }
      }
    }

    return highlights.slice(0, 3) // Return top 3 highlights
  }

  private getCacheKey(query: string, options: any): string {
    return `search_${query}_${JSON.stringify(options)}`
  }

  private generateId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getTopQueries(limit: number): Array<{ query: string; count: number }> {
    const queryCount = new Map<string, number>()
    
    this.searchHistory.forEach(search => {
      const count = queryCount.get(search.query) || 0
      queryCount.set(search.query, count + 1)
    })

    return Array.from(queryCount.entries())
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
  }
}

// Singleton instance
export const searchService = new SearchService()
