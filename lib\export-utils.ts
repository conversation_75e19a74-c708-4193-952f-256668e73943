import type { EmbeddingResult } from "./embedding"
import type { SearchQuery } from "./search-engine"

export interface ExportData {
  embeddings: EmbeddingResult[]
  labels: string[]
  ids: string[]
  similarityMatrix?: number[][]
  searchHistory?: SearchQuery[]
  metadata: {
    exportDate: string
    version: string
    modelUsed: string
    totalDocuments: number
  }
}

export class ExportManager {
  static async exportToJSON(data: ExportData): Promise<string> {
    return JSON.stringify(data, null, 2)
  }

  static async exportToCSV(embeddings: EmbeddingResult[], labels: string[]): Promise<string> {
    const headers = [
      "Label",
      "Text",
      "Processing Time (ms)",
      "Chunks",
      ...Array.from({ length: 384 }, (_, i) => `Dim_${i + 1}`),
    ]

    const rows = embeddings.map((emb, index) => [
      labels[index] || `Text ${index + 1}`,
      `"${emb.text.replace(/"/g, '""')}"`, // Escape quotes in CSV
      emb.processingTime.toString(),
      emb.chunks ? emb.chunks.length.toString() : "1",
      ...emb.embedding.map((val) => val.toFixed(6)),
    ])

    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n")
  }

  static async exportSimilarityMatrixCSV(matrix: number[][], labels: string[]): Promise<string> {
    const headers = ["", ...labels]
    const rows = matrix.map((row, index) => [labels[index] || `Text ${index + 1}`, ...row.map((val) => val.toFixed(6))])

    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n")
  }

  static async exportSearchHistoryJSON(searchHistory: SearchQuery[]): Promise<string> {
    const exportData = searchHistory.map((query) => ({
      query: query.query,
      timestamp: query.timestamp.toISOString(),
      resultsCount: query.results.length,
      processingTime: query.processingTime,
      topResults: query.results.slice(0, 5).map((result) => ({
        label: result.label,
        similarity: result.similarity,
        rank: result.rank,
      })),
    }))

    return JSON.stringify(exportData, null, 2)
  }

  static downloadFile(content: string, filename: string, mimeType = "text/plain") {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  static async importFromJSON(jsonString: string): Promise<ExportData> {
    try {
      const data = JSON.parse(jsonString)

      // Validate the structure
      if (!data.embeddings || !Array.isArray(data.embeddings)) {
        throw new Error("Invalid export format: missing embeddings array")
      }

      if (!data.labels || !Array.isArray(data.labels)) {
        throw new Error("Invalid export format: missing labels array")
      }

      if (!data.metadata) {
        throw new Error("Invalid export format: missing metadata")
      }

      return data as ExportData
    } catch (error) {
      throw new Error(`Failed to import data: ${error instanceof Error ? error.message : "Unknown error"}`)
    }
  }
}

// Caching utilities
export class EmbeddingCache {
  private static readonly CACHE_KEY = "bge-embedding-cache"
  private static readonly MAX_CACHE_SIZE = 100
  private static readonly CACHE_EXPIRY_DAYS = 7

  static saveToCache(text: string, embedding: EmbeddingResult) {
    try {
      const cache = this.getCache()
      const textHash = this.hashText(text)

      cache[textHash] = {
        embedding,
        timestamp: Date.now(),
        textLength: text.length,
      }

      // Limit cache size
      const entries = Object.entries(cache)
      if (entries.length > this.MAX_CACHE_SIZE) {
        // Remove oldest entries
        entries.sort(([, a], [, b]) => a.timestamp - b.timestamp)
        const toKeep = entries.slice(-this.MAX_CACHE_SIZE)
        const newCache: Record<string, any> = {}
        toKeep.forEach(([key, value]) => {
          newCache[key] = value
        })
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(newCache))
      } else {
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache))
      }
    } catch (error) {
      console.warn("Failed to save to cache:", error)
    }
  }

  static getFromCache(text: string): EmbeddingResult | null {
    try {
      const cache = this.getCache()
      const textHash = this.hashText(text)
      const cached = cache[textHash]

      if (!cached) return null

      // Check if cache entry is expired
      const isExpired = Date.now() - cached.timestamp > this.CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000
      if (isExpired) {
        delete cache[textHash]
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache))
        return null
      }

      return cached.embedding
    } catch (error) {
      console.warn("Failed to get from cache:", error)
      return null
    }
  }

  static clearCache() {
    try {
      localStorage.removeItem(this.CACHE_KEY)
    } catch (error) {
      console.warn("Failed to clear cache:", error)
    }
  }

  static getCacheStats() {
    try {
      const cache = this.getCache()
      const entries = Object.values(cache)
      const totalSize = JSON.stringify(cache).length
      const avgTextLength =
        entries.length > 0 ? entries.reduce((sum: number, entry: any) => sum + entry.textLength, 0) / entries.length : 0

      return {
        entryCount: entries.length,
        totalSizeKB: Math.round(totalSize / 1024),
        averageTextLength: Math.round(avgTextLength),
        oldestEntry: entries.length > 0 ? new Date(Math.min(...entries.map((entry: any) => entry.timestamp))) : null,
      }
    } catch (error) {
      return {
        entryCount: 0,
        totalSizeKB: 0,
        averageTextLength: 0,
        oldestEntry: null,
      }
    }
  }

  private static getCache(): Record<string, any> {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY)
      return cached ? JSON.parse(cached) : {}
    } catch (error) {
      return {}
    }
  }

  private static hashText(text: string): string {
    // Simple hash function for text
    let hash = 0
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }
}

// Batch processing utilities
export class BatchProcessor {
  static async processBatch<T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    options: {
      batchSize?: number
      delayMs?: number
      onProgress?: (completed: number, total: number) => void
      onError?: (error: Error, item: T, index: number) => void
    } = {},
  ): Promise<R[]> {
    const { batchSize = 5, delayMs = 100, onProgress, onError } = options
    const results: R[] = []

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      const batchPromises = batch.map(async (item, batchIndex) => {
        const globalIndex = i + batchIndex
        try {
          return await processor(item, globalIndex)
        } catch (error) {
          onError?.(error as Error, item, globalIndex)
          throw error
        }
      })

      try {
        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)

        onProgress?.(results.length, items.length)

        // Add delay between batches to prevent overwhelming the system
        if (i + batchSize < items.length && delayMs > 0) {
          await new Promise((resolve) => setTimeout(resolve, delayMs))
        }
      } catch (error) {
        console.error(`Batch processing failed at batch starting index ${i}:`, error)
        throw error
      }
    }

    return results
  }
}
