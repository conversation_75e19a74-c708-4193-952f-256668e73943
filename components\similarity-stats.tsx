"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, TrendingDown, BarChart3, Target } from "lucide-react"

interface SimilarityStatsProps {
  matrix: number[][]
  labels: string[]
}

export function SimilarityStats({ matrix, labels }: SimilarityStatsProps) {
  if (matrix.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">No similarity data available</p>
        </CardContent>
      </Card>
    )
  }

  // Calculate statistics
  const allSimilarities = matrix.flatMap(
    (row, i) => row.filter((_, j) => i !== j), // Exclude diagonal (self-similarity)
  )

  const avgSimilarity = allSimilarities.reduce((sum, val) => sum + val, 0) / allSimilarities.length
  const maxSimilarity = Math.max(...allSimilarities)
  const minSimilarity = Math.min(...allSimilarities)

  // Find most and least similar pairs
  let mostSimilarPair = { similarity: 0, labels: ["", ""] }
  let leastSimilarPair = { similarity: 1, labels: ["", ""] }

  for (let i = 0; i < matrix.length; i++) {
    for (let j = i + 1; j < matrix.length; j++) {
      const similarity = matrix[i][j]
      if (similarity > mostSimilarPair.similarity) {
        mostSimilarPair = { similarity, labels: [labels[i], labels[j]] }
      }
      if (similarity < leastSimilarPair.similarity) {
        leastSimilarPair = { similarity, labels: [labels[i], labels[j]] }
      }
    }
  }

  // Distribution analysis
  const highSimilarity = allSimilarities.filter((s) => s >= 0.7).length
  const mediumSimilarity = allSimilarities.filter((s) => s >= 0.4 && s < 0.7).length
  const lowSimilarity = allSimilarities.filter((s) => s < 0.4).length

  const highPercentage = (highSimilarity / allSimilarities.length) * 100
  const mediumPercentage = (mediumSimilarity / allSimilarities.length) * 100
  const lowPercentage = (lowSimilarity / allSimilarities.length) * 100

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Similarity</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{(avgSimilarity * 100).toFixed(1)}%</div>
          <Progress value={avgSimilarity * 100} className="mt-2" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Most Similar Pair</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-chart-1">{(mostSimilarPair.similarity * 100).toFixed(1)}%</div>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {mostSimilarPair.labels[0]} ↔ {mostSimilarPair.labels[1]}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Least Similar Pair</CardTitle>
          <TrendingDown className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-chart-4">{(leastSimilarPair.similarity * 100).toFixed(1)}%</div>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {leastSimilarPair.labels[0]} ↔ {leastSimilarPair.labels[1]}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Similarity Range</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{((maxSimilarity - minSimilarity) * 100).toFixed(1)}%</div>
          <p className="text-xs text-muted-foreground mt-1">
            {(minSimilarity * 100).toFixed(1)}% - {(maxSimilarity * 100).toFixed(1)}%
          </p>
        </CardContent>
      </Card>

      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Similarity Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-chart-1 rounded"></div>
                <span className="text-sm">High Similarity (≥70%)</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{highSimilarity} pairs</Badge>
                <span className="text-sm text-muted-foreground">{highPercentage.toFixed(1)}%</span>
              </div>
            </div>
            <Progress value={highPercentage} className="h-2" />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-chart-3 rounded"></div>
                <span className="text-sm">Medium Similarity (40-70%)</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{mediumSimilarity} pairs</Badge>
                <span className="text-sm text-muted-foreground">{mediumPercentage.toFixed(1)}%</span>
              </div>
            </div>
            <Progress value={mediumPercentage} className="h-2" />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-chart-5 rounded"></div>
                <span className="text-sm">Low Similarity (&lt;40%)</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{lowSimilarity} pairs</Badge>
                <span className="text-sm text-muted-foreground">{lowPercentage.toFixed(1)}%</span>
              </div>
            </div>
            <Progress value={lowPercentage} className="h-2" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
