"use client"

import { useCallback, useState } from "react"
import { useDropzone } from "react-dropzone"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, File, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface FileUploadProps {
  onFileContent: (content: string, filename: string) => void
  className?: string
}

export function FileUpload({ onFileContent, className }: FileUploadProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const processFile = useCallback(
    async (file: File) => {
      setIsProcessing(true)
      setError(null)

      try {
        const text = await file.text()
        onFileContent(text, file.name)
      } catch (err) {
        setError(`Failed to read file: ${file.name}`)
        console.error("File processing error:", err)
      } finally {
        setIsProcessing(false)
      }
    },
    [onFileContent],
  )

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      acceptedFiles.forEach(processFile)
    },
    [processFile],
  )

  const { getRootProps, getInputProps, isDragActive, acceptedFiles } = useDropzone({
    onDrop,
    accept: {
      "text/plain": [".txt"],
      "application/pdf": [".pdf"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
    },
    maxFiles: 5,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  return (
    <div className={className}>
      <Card
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed transition-colors cursor-pointer",
          isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary/50",
          isProcessing && "pointer-events-none opacity-50",
        )}
      >
        <CardContent className="flex flex-col items-center justify-center p-8 text-center">
          <input {...getInputProps()} />
          <div className="mb-4">
            <Upload className={cn("h-10 w-10", isDragActive ? "text-primary" : "text-muted-foreground")} />
          </div>
          <div className="space-y-2">
            <p className="text-sm font-medium">
              {isDragActive ? "Drop files here..." : "Drag & drop files or click to browse"}
            </p>
            <p className="text-xs text-muted-foreground">Supports TXT, PDF, and DOCX files (max 10MB each)</p>
          </div>
          {isProcessing && <div className="mt-4 text-sm text-muted-foreground">Processing files...</div>}
        </CardContent>
      </Card>

      {error && (
        <div className="mt-2 flex items-center gap-2 text-sm text-destructive">
          <AlertCircle className="h-4 w-4" />
          {error}
        </div>
      )}

      {acceptedFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          <p className="text-sm font-medium">Uploaded Files:</p>
          {acceptedFiles.map((file, index) => (
            <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
              <File className="h-4 w-4" />
              <span>{file.name}</span>
              <span className="text-xs">({(file.size / 1024).toFixed(1)} KB)</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
