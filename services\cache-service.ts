// Professional Cache Service with LRU and TTL Support

interface CacheEntry<T> {
  value: T
  timestamp: number
  accessCount: number
  lastAccessed: number
}

interface CacheStats {
  size: number
  maxSize: number
  hitRate: number
  totalHits: number
  totalMisses: number
  oldestEntry: number
  newestEntry: number
}

export class CacheService<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private accessOrder: string[] = []
  private hits = 0
  private misses = 0

  constructor(
    private key: string,
    private maxSize: number = 1000,
    private ttl: number = 24 * 60 * 60 * 1000 // 24 hours
  ) {
    this.loadFromStorage()
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.misses++
      return null
    }

    // Check TTL
    if (Date.now() - entry.timestamp > this.ttl) {
      this.delete(key)
      this.misses++
      return null
    }

    // Update access information
    entry.lastAccessed = Date.now()
    entry.accessCount++
    this.updateAccessOrder(key)
    this.hits++

    return entry.value
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T): void {
    const now = Date.now()
    
    // If key exists, update it
    if (this.cache.has(key)) {
      const entry = this.cache.get(key)!
      entry.value = value
      entry.timestamp = now
      entry.lastAccessed = now
      entry.accessCount++
      this.updateAccessOrder(key)
      this.saveToStorage()
      return
    }

    // If cache is full, remove least recently used
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    // Add new entry
    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
    }

    this.cache.set(key, entry)
    this.accessOrder.push(key)
    this.saveToStorage()
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.accessOrder = this.accessOrder.filter(k => k !== key)
      this.saveToStorage()
    }
    return deleted
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false
    
    // Check TTL
    if (Date.now() - entry.timestamp > this.ttl) {
      this.delete(key)
      return false
    }
    
    return true
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.accessOrder = []
    this.hits = 0
    this.misses = 0
    this.clearStorage()
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size
  }

  /**
   * Get all keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get all values
   */
  values(): T[] {
    return Array.from(this.cache.values()).map(entry => entry.value)
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values())
    const timestamps = entries.map(e => e.timestamp)
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hits + this.misses > 0 ? this.hits / (this.hits + this.misses) : 0,
      totalHits: this.hits,
      totalMisses: this.misses,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0,
    }
  }

  /**
   * Clean expired entries
   */
  cleanup(): number {
    const now = Date.now()
    let cleaned = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.delete(key)
        cleaned++
      }
    }
    
    return cleaned
  }

  /**
   * Get entries sorted by access frequency
   */
  getMostAccessed(limit = 10): Array<{ key: string; value: T; accessCount: number }> {
    return Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        value: entry.value,
        accessCount: entry.accessCount,
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit)
  }

  private updateAccessOrder(key: string): void {
    // Move key to end (most recently used)
    this.accessOrder = this.accessOrder.filter(k => k !== key)
    this.accessOrder.push(key)
  }

  private evictLRU(): void {
    if (this.accessOrder.length === 0) return
    
    const lruKey = this.accessOrder[0]
    this.delete(lruKey)
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const data = {
        cache: Array.from(this.cache.entries()),
        accessOrder: this.accessOrder,
        hits: this.hits,
        misses: this.misses,
        timestamp: Date.now(),
      }

      localStorage.setItem(this.key, JSON.stringify(data))
    } catch (error) {
      // Silently fail on server side or when localStorage is not available
      if (typeof window !== 'undefined') {
        console.warn('Failed to save cache to storage:', error)
      }
    }
  }

  private loadFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem(this.key)
      if (!stored) return

      const data = JSON.parse(stored)

      // Check if data is not too old
      if (Date.now() - data.timestamp > this.ttl) {
        this.clearStorage()
        return
      }

      // Restore cache
      this.cache = new Map(data.cache)
      this.accessOrder = data.accessOrder || []
      this.hits = data.hits || 0
      this.misses = data.misses || 0

      // Clean expired entries
      this.cleanup()
    } catch (error) {
      // Silently fail on server side or when localStorage is not available
      if (typeof window !== 'undefined') {
        console.warn('Failed to load cache from storage:', error)
        this.clearStorage()
      }
    }
  }

  private clearStorage(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.removeItem(this.key)
    } catch (error) {
      // Silently fail on server side or when localStorage is not available
      if (typeof window !== 'undefined') {
        console.warn('Failed to clear cache storage:', error)
      }
    }
  }
}

// Utility function to create cache instances
export function createCache<T>(
  key: string,
  maxSize = 1000,
  ttl = 24 * 60 * 60 * 1000
): CacheService<T> {
  return new CacheService<T>(key, maxSize, ttl)
}
