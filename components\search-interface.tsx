"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, Clock, TrendingUp, Filter, X, History, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"
import type { SemanticSearchEngine, SearchQuery, SearchResult } from "@/lib/search-engine"

interface SearchInterfaceProps {
  searchEngine: SemanticSearchEngine
  onResultClick?: (result: SearchResult) => void
  className?: string
}

export function SearchInterface({ searchE<PERSON><PERSON>, onR<PERSON>ultClick, className }: SearchInterfaceProps) {
  const [query, setQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [currentSearch, setCurrentSearch] = useState<SearchQuery | null>(null)
  const [searchHistory, setSearchHistory] = useState<SearchQuery[]>([])
  const [recommendations, setRecommendations] = useState<SearchResult[]>([])
  const [selectedDocId, setSelectedDocId] = useState<string>("")

  // Search parameters
  const [topK, setTopK] = useState([10])
  const [threshold, setThreshold] = useState([0.3])
  const [showAdvanced, setShowAdvanced] = useState(false)

  useEffect(() => {
    setSearchHistory(searchEngine.getSearchHistory())
  }, [searchEngine])

  const handleSearch = async () => {
    if (!query.trim() || isSearching) return

    setIsSearching(true)
    try {
      const result = await searchEngine.search(query, {
        topK: topK[0],
        threshold: threshold[0],
        includeHighlights: true,
      })

      setCurrentSearch(result)
      setSearchHistory(searchEngine.getSearchHistory())
    } catch (error) {
      console.error("Search failed:", error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleRecommendations = async (documentId: string) => {
    if (!documentId) return

    try {
      const recs = await searchEngine.getRecommendations(documentId, 5)
      setRecommendations(recs)
      setSelectedDocId(documentId)
    } catch (error) {
      console.error("Recommendations failed:", error)
    }
  }

  const handleHistorySearch = (historicalQuery: SearchQuery) => {
    setQuery(historicalQuery.query)
    setCurrentSearch(historicalQuery)
  }

  const clearHistory = () => {
    searchEngine.clearSearchHistory()
    setSearchHistory([])
  }

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.8) return "text-chart-1"
    if (similarity >= 0.6) return "text-chart-2"
    if (similarity >= 0.4) return "text-chart-3"
    return "text-chart-4"
  }

  const getSimilarityBadge = (similarity: number) => {
    if (similarity >= 0.8) return "default"
    if (similarity >= 0.6) return "secondary"
    return "outline"
  }

  const corpusStats = searchEngine.getCorpusStats()

  return (
    <div className={className}>
      <Tabs defaultValue="search" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Semantic Search
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Recommendations
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Search History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Semantic Search</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{corpusStats.documentCount} documents</Badge>
                  <Button variant="ghost" size="sm" onClick={() => setShowAdvanced(!showAdvanced)}>
                    <Filter className="h-4 w-4 mr-2" />
                    {showAdvanced ? "Hide" : "Show"} Filters
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search Input */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Enter your search query..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                    className="pl-10"
                  />
                </div>
                <Button onClick={handleSearch} disabled={!query.trim() || isSearching}>
                  {isSearching ? "Searching..." : "Search"}
                </Button>
              </div>

              {/* Advanced Filters */}
              {showAdvanced && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/30 rounded-lg">
                  <div className="space-y-2">
                    <Label>Max Results: {topK[0]}</Label>
                    <Slider value={topK} onValueChange={setTopK} min={1} max={20} step={1} className="w-full" />
                  </div>
                  <div className="space-y-2">
                    <Label>Similarity Threshold: {(threshold[0] * 100).toFixed(0)}%</Label>
                    <Slider
                      value={threshold}
                      onValueChange={setThreshold}
                      min={0}
                      max={1}
                      step={0.05}
                      className="w-full"
                    />
                  </div>
                </div>
              )}

              {/* Search Results */}
              {currentSearch && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Search Results for "{currentSearch.query}"</h3>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      {currentSearch.processingTime}ms
                    </div>
                  </div>

                  {currentSearch.results.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No results found. Try adjusting your search query or lowering the similarity threshold.</p>
                    </div>
                  ) : (
                    <ScrollArea className="h-96">
                      <div className="space-y-3">
                        {currentSearch.results.map((result) => (
                          <Card
                            key={result.id}
                            className={cn(
                              "cursor-pointer transition-colors hover:bg-muted/50",
                              onResultClick && "hover:shadow-md",
                            )}
                            onClick={() => onResultClick?.(result)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">#{result.rank}</Badge>
                                  <h4 className="font-medium">{result.label}</h4>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className={cn("text-sm font-mono", getSimilarityColor(result.similarity))}>
                                    {(result.similarity * 100).toFixed(1)}%
                                  </span>
                                  <Badge variant={getSimilarityBadge(result.similarity)}>
                                    {result.similarity >= 0.8
                                      ? "Excellent"
                                      : result.similarity >= 0.6
                                        ? "Good"
                                        : result.similarity >= 0.4
                                          ? "Fair"
                                          : "Low"}
                                  </Badge>
                                </div>
                              </div>

                              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{result.text}</p>

                              {result.highlights && result.highlights.length > 0 && (
                                <div className="space-y-1">
                                  <p className="text-xs font-medium text-muted-foreground">Relevant excerpts:</p>
                                  {result.highlights.map((highlight, index) => (
                                    <p key={index} className="text-xs bg-primary/10 p-2 rounded italic">
                                      "{highlight}..."
                                    </p>
                                  ))}
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Document Recommendations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Select a document to get recommendations:</Label>
                <select
                  className="w-full mt-2 p-2 border rounded-md"
                  value={selectedDocId}
                  onChange={(e) => handleRecommendations(e.target.value)}
                >
                  <option value="">Choose a document...</option>
                  {corpusStats.documentCount > 0 &&
                    Array.from({ length: corpusStats.documentCount }, (_, i) => (
                      <option key={i} value={`doc-${i}`}>
                        Document {i + 1}
                      </option>
                    ))}
                </select>
              </div>

              {recommendations.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-medium">Similar Documents</h3>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {recommendations.map((rec) => (
                        <Card key={rec.id} className="cursor-pointer hover:bg-muted/50">
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium text-sm">{rec.label}</h4>
                              <Badge variant="secondary">{(rec.similarity * 100).toFixed(1)}%</Badge>
                            </div>
                            <p className="text-xs text-muted-foreground line-clamp-2">{rec.text}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Search History</CardTitle>
                {searchHistory.length > 0 && (
                  <Button variant="outline" size="sm" onClick={clearHistory}>
                    <X className="h-4 w-4 mr-2" />
                    Clear History
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {searchHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No search history yet. Start searching to see your queries here.</p>
                </div>
              ) : (
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {searchHistory.map((search) => (
                      <Card
                        key={search.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleHistorySearch(search)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-1">
                            <p className="font-medium text-sm">"{search.query}"</p>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <TrendingUp className="h-3 w-3" />
                              {search.results.length} results
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {search.timestamp.toLocaleString()} • {search.processingTime}ms
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
