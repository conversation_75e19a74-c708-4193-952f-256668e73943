// Simple PCA implementation for dimensionality reduction
export interface PCAResult {
  reducedData: number[][]
  explainedVariance: number[]
  totalVariance: number
}

export function performPCA(data: number[][], targetDimensions = 3): PCAResult {
  const n = data.length
  const d = data[0].length

  // Center the data
  const means = new Array(d).fill(0)
  for (let i = 0; i < n; i++) {
    for (let j = 0; j < d; j++) {
      means[j] += data[i][j]
    }
  }
  for (let j = 0; j < d; j++) {
    means[j] /= n
  }

  const centeredData = data.map((row) => row.map((val, j) => val - means[j]))

  // Compute covariance matrix (simplified for performance)
  // For high-dimensional data, we use a more efficient approach
  const covMatrix = new Array(d).fill(0).map(() => new Array(d).fill(0))

  for (let i = 0; i < d; i++) {
    for (let j = i; j < d; j++) {
      let sum = 0
      for (let k = 0; k < n; k++) {
        sum += centeredData[k][i] * centeredData[k][j]
      }
      covMatrix[i][j] = sum / (n - 1)
      covMatrix[j][i] = covMatrix[i][j]
    }
  }

  // Simplified eigenvalue decomposition using power iteration
  // This is a basic implementation - in production, use a proper linear algebra library
  const eigenvectors = []
  const eigenvalues = []

  for (let comp = 0; comp < Math.min(targetDimensions, 10); comp++) {
    let vector = new Array(d).fill(0).map(() => Math.random() - 0.5)

    // Power iteration
    for (let iter = 0; iter < 100; iter++) {
      const newVector = new Array(d).fill(0)

      // Matrix-vector multiplication
      for (let i = 0; i < d; i++) {
        for (let j = 0; j < d; j++) {
          newVector[i] += covMatrix[i][j] * vector[j]
        }
      }

      // Orthogonalize against previous eigenvectors
      for (const prevEigenvector of eigenvectors) {
        const dot = newVector.reduce((sum, val, i) => sum + val * prevEigenvector[i], 0)
        for (let i = 0; i < d; i++) {
          newVector[i] -= dot * prevEigenvector[i]
        }
      }

      // Normalize
      const norm = Math.sqrt(newVector.reduce((sum, val) => sum + val * val, 0))
      if (norm > 1e-10) {
        vector = newVector.map((val) => val / norm)
      }
    }

    // Calculate eigenvalue
    const eigenvalue = vector.reduce((sum, val, i) => {
      const matrixRow = covMatrix[i].reduce((rowSum, covVal, j) => rowSum + covVal * vector[j], 0)
      return sum + val * matrixRow
    }, 0)

    eigenvectors.push(vector)
    eigenvalues.push(Math.max(0, eigenvalue))
  }

  // Project data onto principal components
  const reducedData = centeredData.map((row) => {
    return eigenvectors
      .slice(0, targetDimensions)
      .map((eigenvector) => row.reduce((sum, val, i) => sum + val * eigenvector[i], 0))
  })

  const totalVariance = eigenvalues.reduce((sum, val) => sum + val, 0)
  const explainedVariance = eigenvalues.slice(0, targetDimensions).map((val) => val / totalVariance)

  return {
    reducedData,
    explainedVariance,
    totalVariance,
  }
}

// Simple t-SNE implementation (basic version)
export function performTSNE(data: number[][], targetDimensions = 3, perplexity = 30): number[][] {
  const n = data.length

  // Initialize random positions
  const Y = new Array(n).fill(0).map(() => new Array(targetDimensions).fill(0).map(() => (Math.random() - 0.5) * 1e-4))

  // Calculate pairwise distances in high-dimensional space
  const distances = new Array(n).fill(0).map(() => new Array(n).fill(0))
  for (let i = 0; i < n; i++) {
    for (let j = i + 1; j < n; j++) {
      const dist = Math.sqrt(data[i].reduce((sum, val, k) => sum + Math.pow(val - data[j][k], 2), 0))
      distances[i][j] = dist
      distances[j][i] = dist
    }
  }

  // Convert distances to probabilities (simplified)
  const P = new Array(n).fill(0).map(() => new Array(n).fill(0))
  const sigma = 1.0 // Simplified - should be computed based on perplexity

  for (let i = 0; i < n; i++) {
    let sum = 0
    for (let j = 0; j < n; j++) {
      if (i !== j) {
        P[i][j] = Math.exp((-distances[i][j] * distances[i][j]) / (2 * sigma * sigma))
        sum += P[i][j]
      }
    }
    // Normalize
    for (let j = 0; j < n; j++) {
      if (i !== j) {
        P[i][j] /= sum
      }
    }
  }

  // Simplified gradient descent (basic implementation)
  const learningRate = 200
  const momentum = 0.8
  const gains = new Array(n).fill(0).map(() => new Array(targetDimensions).fill(1))
  const velocities = new Array(n).fill(0).map(() => new Array(targetDimensions).fill(0))

  for (let iter = 0; iter < 100; iter++) {
    // Calculate Q matrix (probabilities in low-dimensional space)
    const Q = new Array(n).fill(0).map(() => new Array(n).fill(0))
    let sumQ = 0

    for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
        const dist = Math.sqrt(Y[i].reduce((sum, val, k) => sum + Math.pow(val - Y[j][k], 2), 0))
        const q = 1 / (1 + dist * dist)
        Q[i][j] = q
        Q[j][i] = q
        sumQ += 2 * q
      }
    }

    // Normalize Q
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        Q[i][j] = Math.max(Q[i][j] / sumQ, 1e-12)
      }
    }

    // Calculate gradients and update positions
    for (let i = 0; i < n; i++) {
      for (let d = 0; d < targetDimensions; d++) {
        let gradient = 0

        for (let j = 0; j < n; j++) {
          if (i !== j) {
            const diff = Y[i][d] - Y[j][d]
            const dist = Math.sqrt(Y[i].reduce((sum, val, k) => sum + Math.pow(val - Y[j][k], 2), 0))
            const factor = ((P[i][j] - Q[i][j]) * diff) / (1 + dist * dist)
            gradient += 4 * factor
          }
        }

        // Update velocity and position
        velocities[i][d] = momentum * velocities[i][d] - learningRate * gains[i][d] * gradient
        Y[i][d] += velocities[i][d]

        // Update gains
        gains[i][d] = Math.max(
          0.01,
          Math.sign(gradient) !== Math.sign(velocities[i][d]) ? gains[i][d] + 0.2 : gains[i][d] * 0.8,
        )
      }
    }
  }

  return Y
}

// Clustering using k-means for color coding
export function performKMeansClustering(data: number[][], k = 3): number[] {
  const n = data.length
  const d = data[0].length

  // Initialize centroids randomly
  const centroids = new Array(k).fill(0).map(() => new Array(d).fill(0).map(() => Math.random() * 2 - 1))

  let assignments = new Array(n).fill(0)

  for (let iter = 0; iter < 50; iter++) {
    // Assign points to nearest centroid
    const newAssignments = new Array(n)

    for (let i = 0; i < n; i++) {
      let minDist = Number.POSITIVE_INFINITY
      let bestCluster = 0

      for (let j = 0; j < k; j++) {
        const dist = Math.sqrt(data[i].reduce((sum, val, dim) => sum + Math.pow(val - centroids[j][dim], 2), 0))
        if (dist < minDist) {
          minDist = dist
          bestCluster = j
        }
      }

      newAssignments[i] = bestCluster
    }

    // Update centroids
    for (let j = 0; j < k; j++) {
      const clusterPoints = data.filter((_, i) => newAssignments[i] === j)
      if (clusterPoints.length > 0) {
        for (let dim = 0; dim < d; dim++) {
          centroids[j][dim] = clusterPoints.reduce((sum, point) => sum + point[dim], 0) / clusterPoints.length
        }
      }
    }

    // Check for convergence
    if (assignments.every((val, i) => val === newAssignments[i])) {
      break
    }

    assignments = newAssignments
  }

  return assignments
}
