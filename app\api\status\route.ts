import { NextResponse } from "next/server"
import { spawn } from "child_process"
import { APP_CONFIG, EMBEDDING_CONFIG, PYTHON_CONFIG } from "@/constants"
import type { APIStatus } from "@/types"

export async function GET() {
  const startTime = Date.now()
  
  // Check Python service availability
  let pythonAvailable = false
  let pythonError: string | null = null
  
  try {
    await checkPythonService()
    pythonAvailable = true
  } catch (error) {
    pythonError = error instanceof Error ? error.message : "Unknown error"
  }

  const status: APIStatus = {
    status: "operational",
    timestamp: new Date().toISOString(),
    mode: pythonAvailable ? "real" : "mock",
    pythonAvailable,
    requirements: pythonAvailable ? undefined : {
      python: "Python 3.8+",
      packages: PYTHON_CONFIG.requirements,
      install: PYTHON_CONFIG.installCommand,
    },
  }

  const response = {
    ...status,
    app: {
      name: APP_CONFIG.name,
      version: APP_CONFIG.version,
      description: APP_CONFIG.description,
    },
    embedding: {
      defaultModel: EMBEDDING_CONFIG.defaultModel,
      supportedModels: Object.keys(EMBEDDING_CONFIG.models),
      maxTextLength: EMBEDDING_CONFIG.maxTextLength,
      batchSize: EMBEDDING_CONFIG.batchSize,
    },
    performance: {
      responseTime: Date.now() - startTime,
    },
    errors: pythonError ? [pythonError] : [],
  }

  return NextResponse.json(response)
}

async function checkPythonService(): Promise<void> {
  return new Promise((resolve, reject) => {
    const python = spawn("python", [PYTHON_CONFIG.scriptPath, "status"], {
      encoding: PYTHON_CONFIG.encoding as BufferEncoding,
    })

    let output = ""
    let errorOutput = ""

    python.stdout.on("data", (data) => {
      output += data.toString()
    })

    python.stderr.on("data", (data) => {
      errorOutput += data.toString()
    })

    python.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Python service check failed: ${errorOutput}`))
        return
      }

      try {
        const result = JSON.parse(output.trim())
        if (result.status === "ready") {
          resolve()
        } else {
          reject(new Error("Python service not ready"))
        }
      } catch (parseError) {
        reject(new Error(`Failed to parse Python status: ${parseError}`))
      }
    })

    python.on("error", (error) => {
      reject(new Error(`Failed to spawn Python process: ${error.message}`))
    })

    // Set timeout
    setTimeout(() => {
      python.kill()
      reject(new Error("Python service check timeout"))
    }, 5000) // Shorter timeout for status check
  })
}
