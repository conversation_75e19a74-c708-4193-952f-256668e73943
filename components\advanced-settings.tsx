"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { AlertTriangle, Database, Trash2, Upload, Zap } from "lucide-react"
import { EmbeddingCache, ExportManager } from "@/lib/export-utils"

interface AdvancedSettingsProps {
  onImportData?: (data: any) => void
  className?: string
}

export function AdvancedSettings({ onImportData, className }: AdvancedSettingsProps) {
  const [cacheStats, setCacheStats] = useState(EmbeddingCache.getCacheStats())
  const [settings, setSettings] = useState({
    enableCache: true,
    batchSize: 5,
    processingDelay: 100,
    autoSave: true,
    maxCacheSize: 100,
    cacheExpiryDays: 7,
  })
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem("bge-app-settings")
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        setSettings((prev) => ({ ...prev, ...parsed }))
      } catch (error) {
        console.warn("Failed to load settings:", error)
      }
    }
  }, [])

  const saveSettings = () => {
    try {
      localStorage.setItem("bge-app-settings", JSON.stringify(settings))
    } catch (error) {
      console.warn("Failed to save settings:", error)
    }
  }

  const updateSetting = (key: string, value: any) => {
    setSettings((prev) => {
      const newSettings = { ...prev, [key]: value }
      // Auto-save settings
      setTimeout(() => {
        try {
          localStorage.setItem("bge-app-settings", JSON.stringify(newSettings))
        } catch (error) {
          console.warn("Failed to save settings:", error)
        }
      }, 100)
      return newSettings
    })
  }

  const refreshCacheStats = () => {
    setCacheStats(EmbeddingCache.getCacheStats())
  }

  const clearCache = () => {
    EmbeddingCache.clearCache()
    refreshCacheStats()
  }

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    setImportProgress(0)

    try {
      const text = await file.text()
      setImportProgress(50)

      const importedData = await ExportManager.importFromJSON(text)
      setImportProgress(100)

      onImportData?.(importedData)

      setTimeout(() => {
        setIsImporting(false)
        setImportProgress(0)
      }, 1000)
    } catch (error) {
      console.error("Import failed:", error)
      setIsImporting(false)
      setImportProgress(0)
      alert(`Import failed: ${error instanceof Error ? error.message : "Unknown error"}`)
    }

    // Reset file input
    event.target.value = ""
  }

  return (
    <div className={className}>
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="cache" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Cache Management
          </TabsTrigger>
          <TabsTrigger value="import" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import/Export
          </TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Processing Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Enable Embedding Cache</Label>
                  <p className="text-sm text-muted-foreground">
                    Cache embeddings to avoid reprocessing identical texts
                  </p>
                </div>
                <Switch
                  checked={settings.enableCache}
                  onCheckedChange={(checked) => updateSetting("enableCache", checked)}
                />
              </div>

              <div className="space-y-2">
                <Label>Batch Processing Size: {settings.batchSize}</Label>
                <Slider
                  value={[settings.batchSize]}
                  onValueChange={([value]) => updateSetting("batchSize", value)}
                  min={1}
                  max={20}
                  step={1}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">Number of texts to process simultaneously</p>
              </div>

              <div className="space-y-2">
                <Label>Processing Delay: {settings.processingDelay}ms</Label>
                <Slider
                  value={[settings.processingDelay]}
                  onValueChange={([value]) => updateSetting("processingDelay", value)}
                  min={0}
                  max={1000}
                  step={50}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Delay between batch processing to prevent overwhelming the system
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Auto-save Results</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically save embeddings and results to local storage
                  </p>
                </div>
                <Switch checked={settings.autoSave} onCheckedChange={(checked) => updateSetting("autoSave", checked)} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Cache Statistics</CardTitle>
                <Button variant="outline" size="sm" onClick={refreshCacheStats}>
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{cacheStats.entryCount}</div>
                  <div className="text-sm text-muted-foreground">Cached Items</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{cacheStats.totalSizeKB}</div>
                  <div className="text-sm text-muted-foreground">KB Used</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{cacheStats.averageTextLength}</div>
                  <div className="text-sm text-muted-foreground">Avg Length</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {cacheStats.oldestEntry
                      ? Math.floor((Date.now() - cacheStats.oldestEntry.getTime()) / (1000 * 60 * 60 * 24))
                      : 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Days Old</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cache Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Maximum Cache Size: {settings.maxCacheSize} items</Label>
                <Slider
                  value={[settings.maxCacheSize]}
                  onValueChange={([value]) => updateSetting("maxCacheSize", value)}
                  min={10}
                  max={500}
                  step={10}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Cache Expiry: {settings.cacheExpiryDays} days</Label>
                <Slider
                  value={[settings.cacheExpiryDays]}
                  onValueChange={([value]) => updateSetting("cacheExpiryDays", value)}
                  min={1}
                  max={30}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <AlertTriangle className="h-4 w-4" />
                  Clear all cached embeddings
                </div>
                <Button variant="destructive" size="sm" onClick={clearCache}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Cache
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Import Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="font-medium">Import Previous Export</p>
                  <p className="text-sm text-muted-foreground">Upload a JSON file exported from this application</p>
                </div>
                <div className="mt-4">
                  <Input
                    type="file"
                    accept=".json"
                    onChange={handleFileImport}
                    disabled={isImporting}
                    className="max-w-xs mx-auto"
                  />
                </div>
              </div>

              {isImporting && (
                <div className="space-y-2">
                  <Progress value={importProgress} className="w-full" />
                  <div className="text-xs text-center text-muted-foreground">
                    {importProgress < 100 ? "Processing import..." : "Import complete!"}
                  </div>
                </div>
              )}

              <div className="bg-muted/30 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Import Requirements</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• File must be in JSON format</li>
                  <li>• Must contain valid embedding data</li>
                  <li>• Compatible with exports from this application</li>
                  <li>• Maximum file size: 50MB</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Backup & Restore</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Application Settings</p>
                    <p className="text-sm text-muted-foreground">Export your current settings and preferences</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const settingsBlob = new Blob([JSON.stringify(settings, null, 2)], { type: "application/json" })
                      const url = URL.createObjectURL(settingsBlob)
                      const link = document.createElement("a")
                      link.href = url
                      link.download = `bge-settings-${new Date().toISOString().split("T")[0]}.json`
                      link.click()
                      URL.revokeObjectURL(url)
                    }}
                  >
                    Export Settings
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Reset to Defaults</p>
                    <p className="text-sm text-muted-foreground">Restore all settings to their default values</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const defaultSettings = {
                        enableCache: true,
                        batchSize: 5,
                        processingDelay: 100,
                        autoSave: true,
                        maxCacheSize: 100,
                        cacheExpiryDays: 7,
                      }
                      setSettings(defaultSettings)
                      localStorage.setItem("bge-app-settings", JSON.stringify(defaultSettings))
                    }}
                  >
                    Reset Settings
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
