"use client"

import type React from "react"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Download, FileText, Database, History, Settings } from "lucide-react"
import { ExportManager, type ExportData } from "@/lib/export-utils"
import type { EmbeddingResult } from "@/lib/embedding"
import type { SearchQuery } from "@/lib/search-engine"

interface ExportDialogProps {
  embeddings: EmbeddingResult[]
  labels: string[]
  ids: string[]
  similarityMatrix: number[][]
  searchHistory: SearchQuery[]
  trigger?: React.ReactNode
}

export function ExportDialog({ embeddings, labels, ids, similarityMatrix, searchHistory, trigger }: ExportDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)

  const [exportOptions, setExportOptions] = useState({
    includeEmbeddings: true,
    includeSimilarityMatrix: true,
    includeSearchHistory: true,
    includeMetadata: true,
    format: "json" as "json" | "csv",
  })

  const handleExport = async () => {
    if (embeddings.length === 0) return

    setIsExporting(true)
    setExportProgress(0)

    try {
      const timestamp = new Date().toISOString().split("T")[0]

      if (exportOptions.format === "json") {
        const exportData: ExportData = {
          embeddings: exportOptions.includeEmbeddings ? embeddings : [],
          labels,
          ids,
          similarityMatrix: exportOptions.includeSimilarityMatrix ? similarityMatrix : undefined,
          searchHistory: exportOptions.includeSearchHistory ? searchHistory : undefined,
          metadata: {
            exportDate: new Date().toISOString(),
            version: "1.0.0",
            modelUsed: "BAAI/bge-small-en-v1.5",
            totalDocuments: embeddings.length,
          },
        }

        setExportProgress(50)
        const jsonContent = await ExportManager.exportToJSON(exportData)
        setExportProgress(100)

        ExportManager.downloadFile(jsonContent, `bge-embeddings-${timestamp}.json`, "application/json")
      } else {
        // CSV export
        if (exportOptions.includeEmbeddings) {
          setExportProgress(25)
          const csvContent = await ExportManager.exportToCSV(embeddings, labels)
          setExportProgress(50)
          ExportManager.downloadFile(csvContent, `bge-embeddings-${timestamp}.csv`, "text/csv")
        }

        if (exportOptions.includeSimilarityMatrix && similarityMatrix.length > 0) {
          setExportProgress(75)
          const matrixCsv = await ExportManager.exportSimilarityMatrixCSV(similarityMatrix, labels)
          ExportManager.downloadFile(matrixCsv, `similarity-matrix-${timestamp}.csv`, "text/csv")
        }

        if (exportOptions.includeSearchHistory && searchHistory.length > 0) {
          const historyJson = await ExportManager.exportSearchHistoryJSON(searchHistory)
          ExportManager.downloadFile(historyJson, `search-history-${timestamp}.json`, "application/json")
        }

        setExportProgress(100)
      }

      // Close dialog after successful export
      setTimeout(() => {
        setIsOpen(false)
        setIsExporting(false)
        setExportProgress(0)
      }, 1000)
    } catch (error) {
      console.error("Export failed:", error)
      setIsExporting(false)
      setExportProgress(0)
    }
  }

  const getExportSize = () => {
    let size = 0
    if (exportOptions.includeEmbeddings) {
      size += embeddings.length * 384 * 8 // Rough estimate: 384 dimensions * 8 bytes per float
    }
    if (exportOptions.includeSimilarityMatrix) {
      size += similarityMatrix.length * similarityMatrix.length * 8
    }
    if (exportOptions.includeSearchHistory) {
      size += searchHistory.reduce((sum, query) => sum + JSON.stringify(query).length, 0)
    }
    return Math.round(size / 1024) // Convert to KB
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Export Embedding Data</DialogTitle>
          <DialogDescription>
            Export your embeddings, similarity analysis, and search history for backup or sharing
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="options" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="options">Export Options</TabsTrigger>
            <TabsTrigger value="preview">Data Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="options" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What to Export</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="embeddings"
                      checked={exportOptions.includeEmbeddings}
                      onCheckedChange={(checked) =>
                        setExportOptions((prev) => ({ ...prev, includeEmbeddings: !!checked }))
                      }
                    />
                    <Label htmlFor="embeddings" className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Embeddings ({embeddings.length} items)
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="similarity"
                      checked={exportOptions.includeSimilarityMatrix}
                      onCheckedChange={(checked) =>
                        setExportOptions((prev) => ({ ...prev, includeSimilarityMatrix: !!checked }))
                      }
                      disabled={similarityMatrix.length === 0}
                    />
                    <Label htmlFor="similarity" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Similarity Matrix ({similarityMatrix.length}×{similarityMatrix.length})
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="history"
                      checked={exportOptions.includeSearchHistory}
                      onCheckedChange={(checked) =>
                        setExportOptions((prev) => ({ ...prev, includeSearchHistory: !!checked }))
                      }
                      disabled={searchHistory.length === 0}
                    />
                    <Label htmlFor="history" className="flex items-center gap-2">
                      <History className="h-4 w-4" />
                      Search History ({searchHistory.length} queries)
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="metadata"
                      checked={exportOptions.includeMetadata}
                      onCheckedChange={(checked) =>
                        setExportOptions((prev) => ({ ...prev, includeMetadata: !!checked }))
                      }
                    />
                    <Label htmlFor="metadata" className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Metadata & Settings
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Export Format</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="json"
                      name="format"
                      checked={exportOptions.format === "json"}
                      onChange={() => setExportOptions((prev) => ({ ...prev, format: "json" }))}
                    />
                    <Label htmlFor="json">
                      <div>
                        <div className="font-medium">JSON (Recommended)</div>
                        <div className="text-sm text-muted-foreground">Complete data with metadata</div>
                      </div>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="csv"
                      name="format"
                      checked={exportOptions.format === "csv"}
                      onChange={() => setExportOptions((prev) => ({ ...prev, format: "csv" }))}
                    />
                    <Label htmlFor="csv">
                      <div>
                        <div className="font-medium">CSV</div>
                        <div className="text-sm text-muted-foreground">Spreadsheet compatible</div>
                      </div>
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
              <div>
                <div className="font-medium">Estimated Export Size</div>
                <div className="text-sm text-muted-foreground">Approximately {getExportSize()} KB</div>
              </div>
              <Badge variant="outline">{embeddings.length} documents</Badge>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Embeddings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{embeddings.length}</div>
                  <div className="text-xs text-muted-foreground">384-dimensional vectors</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Similarity Pairs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {similarityMatrix.length > 0 ? (similarityMatrix.length * (similarityMatrix.length - 1)) / 2 : 0}
                  </div>
                  <div className="text-xs text-muted-foreground">Unique comparisons</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Search Queries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{searchHistory.length}</div>
                  <div className="text-xs text-muted-foreground">Historical searches</div>
                </CardContent>
              </Card>
            </div>

            {embeddings.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Sample Data Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-xs font-mono">
                    <div>
                      Labels: [
                      {labels
                        .slice(0, 3)
                        .map((l) => `"${l}"`)
                        .join(", ")}
                      ...]
                    </div>
                    <div>
                      First embedding: [
                      {embeddings[0].embedding
                        .slice(0, 5)
                        .map((v) => v.toFixed(3))
                        .join(", ")}
                      ...]
                    </div>
                    {similarityMatrix.length > 0 && (
                      <div>
                        Similarity sample: [
                        {similarityMatrix[0]
                          .slice(0, 3)
                          .map((v) => v.toFixed(3))
                          .join(", ")}
                        ...]
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center pt-4">
          <div className="text-sm text-muted-foreground">
            {embeddings.length === 0 ? "No data to export" : "Ready to export"}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={embeddings.length === 0 || isExporting}>
              {isExporting ? (
                <>
                  <Download className="h-4 w-4 mr-2 animate-pulse" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </>
              )}
            </Button>
          </div>
        </div>

        {isExporting && (
          <div className="space-y-2">
            <Progress value={exportProgress} className="w-full" />
            <div className="text-xs text-center text-muted-foreground">
              {exportProgress < 100 ? "Preparing export..." : "Download starting..."}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
