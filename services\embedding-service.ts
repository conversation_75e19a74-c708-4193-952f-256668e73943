// Professional Embedding Service with Caching and Batch Processing

import { api<PERSON><PERSON>, APIError, getErrorInfo } from './api-client'
import { CacheService } from './cache-service'
import { EMBEDDING_CONFIG, VALIDATION_RULES, CACHE_CONFIG } from '@/constants'
import type { 
  EmbeddingResult, 
  ProcessedText, 
  SimilarityResult,
  ProcessingProgress,
  ErrorInfo 
} from '@/types'

export class EmbeddingService {
  private cache: CacheService<EmbeddingResult>
  private processingQueue: Map<string, Promise<EmbeddingResult>> = new Map()

  constructor() {
    this.cache = new CacheService<EmbeddingResult>(
      CACHE_CONFIG.embeddings.key,
      CACHE_CONFIG.embeddings.maxSize,
      CACHE_CONFIG.embeddings.ttl
    )
  }

  /**
   * Generate embedding for a single text with caching
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    // Validate input
    if (!this.validateText(text)) {
      throw new APIError('Invalid text input', 400, 'VALIDATION_ERROR')
    }

    // Check cache first
    const cacheKey = this.getCacheKey(text)
    const cached = this.cache.get(cacheKey)
    if (cached) {
      return cached
    }

    // Check if already processing
    const existingPromise = this.processingQueue.get(cacheKey)
    if (existingPromise) {
      return existingPromise
    }

    // Create new processing promise
    const promise = this.processEmbedding(text, cacheKey)
    this.processingQueue.set(cacheKey, promise)

    try {
      const result = await promise
      this.cache.set(cacheKey, result)
      return result
    } finally {
      this.processingQueue.delete(cacheKey)
    }
  }

  /**
   * Generate embeddings for multiple texts with progress tracking
   */
  async generateBatchEmbeddings(
    texts: string[],
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<EmbeddingResult[]> {
    if (!Array.isArray(texts) || texts.length === 0) {
      throw new APIError('Invalid texts array', 400, 'VALIDATION_ERROR')
    }

    if (texts.length > VALIDATION_RULES.batch.maxSize) {
      throw new APIError(
        `Batch size exceeds maximum of ${VALIDATION_RULES.batch.maxSize}`,
        400,
        'BATCH_SIZE_ERROR'
      )
    }

    const results: EmbeddingResult[] = []
    const errors: ErrorInfo[] = []
    
    // Check cache for existing embeddings
    const uncachedTexts: string[] = []
    const uncachedIndices: number[] = []
    
    for (let i = 0; i < texts.length; i++) {
      const text = texts[i]
      if (!this.validateText(text)) {
        errors.push({
          code: 'VALIDATION_ERROR',
          message: `Invalid text at index ${i}`,
          timestamp: new Date(),
        })
        continue
      }

      const cacheKey = this.getCacheKey(text)
      const cached = this.cache.get(cacheKey)
      
      if (cached) {
        results[i] = cached
      } else {
        uncachedTexts.push(text)
        uncachedIndices.push(i)
      }
    }

    // Process uncached texts in batches
    if (uncachedTexts.length > 0) {
      const batchSize = EMBEDDING_CONFIG.batchSize
      const totalBatches = Math.ceil(uncachedTexts.length / batchSize)

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const start = batchIndex * batchSize
        const end = Math.min(start + batchSize, uncachedTexts.length)
        const batchTexts = uncachedTexts.slice(start, end)
        const batchIndices = uncachedIndices.slice(start, end)

        try {
          onProgress?.({
            current: batchIndex + 1,
            total: totalBatches,
            stage: 'processing',
            message: `Processing batch ${batchIndex + 1} of ${totalBatches}`,
          })

          const batchResponse = await apiClient.generateBatchEmbeddings(batchTexts)
          
          // Process batch results
          for (let i = 0; i < batchTexts.length; i++) {
            const text = batchTexts[i]
            const originalIndex = batchIndices[i]
            
            const embeddingResult: EmbeddingResult = {
              text,
              embedding: batchResponse.embeddings[i],
              processingTime: batchResponse.processingTime / batchTexts.length,
              dimension: batchResponse.dimension,
              model: batchResponse.model,
              note: batchResponse.note,
              fallback: batchResponse.fallback,
              requirements: batchResponse.requirements,
            }

            results[originalIndex] = embeddingResult
            
            // Cache the result
            const cacheKey = this.getCacheKey(text)
            this.cache.set(cacheKey, embeddingResult)
          }
        } catch (error) {
          // Handle batch errors
          for (const index of batchIndices) {
            errors.push(getErrorInfo(error))
          }
        }
      }
    }

    onProgress?.({
      current: texts.length,
      total: texts.length,
      stage: 'complete',
      message: 'Processing complete',
    })

    // Fill any missing results with errors
    for (let i = 0; i < texts.length; i++) {
      if (!results[i] && errors.length > 0) {
        throw new APIError(
          `Failed to process text at index ${i}: ${errors[0].message}`,
          500,
          'BATCH_PROCESSING_ERROR'
        )
      }
    }

    return results
  }

  /**
   * Calculate similarity between two embeddings
   */
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimension')
    }

    // Cosine similarity
    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i]
      norm1 += embedding1[i] * embedding1[i]
      norm2 += embedding2[i] * embedding2[i]
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  /**
   * Find most similar embeddings
   */
  findMostSimilar(
    queryEmbedding: EmbeddingResult,
    corpus: EmbeddingResult[],
    topK = 10
  ): SimilarityResult[] {
    const similarities = corpus.map((result, index) => ({
      similarity: this.calculateSimilarity(queryEmbedding.embedding, result.embedding),
      result,
      index,
    }))

    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK)
  }

  /**
   * Generate similarity matrix for a set of embeddings
   */
  generateSimilarityMatrix(embeddings: EmbeddingResult[]): number[][] {
    const matrix: number[][] = []
    
    for (let i = 0; i < embeddings.length; i++) {
      matrix[i] = []
      for (let j = 0; j < embeddings.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0
        } else {
          matrix[i][j] = this.calculateSimilarity(
            embeddings[i].embedding,
            embeddings[j].embedding
          )
        }
      }
    }

    return matrix
  }

  /**
   * Clear embedding cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cache.getStats()
  }

  private async processEmbedding(text: string, cacheKey: string): Promise<EmbeddingResult> {
    try {
      return await apiClient.generateEmbedding(text)
    } catch (error) {
      throw error
    }
  }

  private validateText(text: string): boolean {
    return (
      typeof text === 'string' &&
      text.length >= VALIDATION_RULES.text.minLength &&
      text.length <= VALIDATION_RULES.text.maxLength
    )
  }

  private getCacheKey(text: string): string {
    // Simple hash function for cache key
    let hash = 0
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return `embedding_${hash.toString(36)}`
  }
}

// Singleton instance
export const embeddingService = new EmbeddingService()
