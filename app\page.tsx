"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  Loader2, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  Plus, 
  Trash2, 
  Settings, 
  Download, 
  Upload,
  Brain,
  Zap,
  BarChart3,
  Search,
  RefreshCw
} from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import { TextInputCard } from "@/components/text-input-card"
import { SimilarityMatrix } from "@/components/similarity-matrix"
import { SimilarityPairs } from "@/components/similarity-pairs"
import { SimilarityStats } from "@/components/similarity-stats"
import { Embedding3DVisualization } from "@/components/embedding-3d-visualization"
import { SearchInterface } from "@/components/search-interface"
import { ExportDialog } from "@/components/export-dialog"
import { AdvancedSettings } from "@/components/advanced-settings"
import { PythonRequirementsNotice } from "@/components/python-requirements-notice"
import { embeddingService, searchService, apiClient } from "@/services"
import { APP_CONFIG, VALIDATION_RULES } from "@/constants"
import type { ProcessedText, EmbeddingResult, ProcessingProgress, APIStatus } from "@/types"

export default function EmbeddingVisualizerPage() {
  // Core state
  const [textInputs, setTextInputs] = useState<ProcessedText[]>([
    { id: "1", text: "", label: "Text 1" }
  ])
  const [embeddings, setEmbeddings] = useState<EmbeddingResult[]>([])
  const [similarityMatrix, setSimilarityMatrix] = useState<number[][]>([])
  
  // Processing state
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState<ProcessingProgress>({
    current: 0,
    total: 0,
    stage: 'idle',
    message: 'Ready to process'
  })
  
  // API state
  const [apiStatus, setApiStatus] = useState<APIStatus | null>(null)
  const [showRequirements, setShowRequirements] = useState(false)
  
  // UI state
  const [activeTab, setActiveTab] = useState("input")
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)

  // Initialize API status check
  useEffect(() => {
    const checkAPIStatus = async () => {
      try {
        const status = await apiClient.getStatus()
        setApiStatus(status)
        setShowRequirements(!status.pythonAvailable)
      } catch (error) {
        console.error("Failed to check API status:", error)
        setShowRequirements(true)
      }
    }

    checkAPIStatus()
  }, [])

  // Update search engine when embeddings change
  useEffect(() => {
    if (embeddings.length > 0) {
      const labels = textInputs.map(input => input.label)
      const ids = textInputs.map(input => input.id)
      searchService.updateCorpus(embeddings, labels, ids)
    }
  }, [embeddings, textInputs])

  // Text input management
  const addTextInput = () => {
    const newId = Date.now().toString()
    const newLabel = `Text ${textInputs.length + 1}`
    setTextInputs([...textInputs, { id: newId, text: "", label: newLabel }])
  }

  const removeTextInput = (id: string) => {
    if (textInputs.length > 1) {
      const index = textInputs.findIndex(input => input.id === id)
      setTextInputs(textInputs.filter(input => input.id !== id))
      
      // Remove corresponding embedding
      if (index !== -1) {
        setEmbeddings(prev => prev.filter((_, i) => i !== index))
        setSimilarityMatrix(prev => {
          const newMatrix = prev.filter((_, i) => i !== index)
          return newMatrix.map(row => row.filter((_, j) => j !== index))
        })
      }
    }
  }

  const updateTextInput = (id: string, text: string) => {
    setTextInputs(textInputs.map(input => 
      input.id === id ? { ...input, text } : input
    ))
  }

  const updateTextLabel = (id: string, label: string) => {
    setTextInputs(textInputs.map(input => 
      input.id === id ? { ...input, label } : input
    ))
  }

  // File handling
  const handleFileContent = (content: string, filename: string) => {
    const newId = Date.now().toString()
    const label = filename.replace(/\.[^/.]+$/, "")
    setTextInputs([...textInputs, { id: newId, text: content, label }])
  }

  // Embedding processing
  const processEmbeddings = async () => {
    const validTexts = textInputs.filter(input => input.text.trim().length > 0)
    
    if (validTexts.length === 0) {
      alert("Please enter at least one text to process")
      return
    }

    setIsProcessing(true)
    setProgress({
      current: 0,
      total: validTexts.length,
      stage: 'processing',
      message: 'Starting embedding generation...'
    })

    try {
      const texts = validTexts.map(input => input.text)
      
      const results = await embeddingService.generateBatchEmbeddings(
        texts,
        (progressUpdate) => {
          setProgress(progressUpdate)
        }
      )

      setEmbeddings(results)
      
      // Generate similarity matrix
      const matrix = embeddingService.generateSimilarityMatrix(results)
      setSimilarityMatrix(matrix)
      
      // Switch to results tab
      setActiveTab("similarity")
      
      setProgress({
        current: validTexts.length,
        total: validTexts.length,
        stage: 'complete',
        message: 'Processing complete!'
      })
    } catch (error) {
      console.error("Failed to process embeddings:", error)
      alert(`Failed to process embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsProcessing(false)
    }
  }

  // Clear all data
  const clearAll = () => {
    setTextInputs([{ id: "1", text: "", label: "Text 1" }])
    setEmbeddings([])
    setSimilarityMatrix([])
    setActiveTab("input")
    setProgress({
      current: 0,
      total: 0,
      stage: 'idle',
      message: 'Ready to process'
    })
  }

  const validTextCount = textInputs.filter(input => input.text.trim().length > 0).length
  const hasEmbeddings = embeddings.length > 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
                  {APP_CONFIG.name}
                </h1>
                <p className="text-slate-600 dark:text-slate-400">
                  {APP_CONFIG.description}
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {apiStatus && (
              <Badge 
                variant={apiStatus.pythonAvailable ? "default" : "secondary"}
                className="flex items-center space-x-1"
              >
                {apiStatus.pythonAvailable ? (
                  <CheckCircle className="h-3 w-3" />
                ) : (
                  <AlertTriangle className="h-3 w-3" />
                )}
                <span>{apiStatus.mode === 'real' ? 'BGE Ready' : 'Demo Mode'}</span>
              </Badge>
            )}
            <ThemeToggle />
          </div>
        </div>

        {/* Requirements Notice */}
        {showRequirements && (
          <div className="mb-6">
            <PythonRequirementsNotice onDismiss={() => setShowRequirements(false)} />
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Control Panel */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>Control Panel</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <Button
                    onClick={processEmbeddings}
                    disabled={isProcessing || validTextCount === 0}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        Generate Embeddings
                      </>
                    )}
                  </Button>
                  
                  <Button
                    onClick={clearAll}
                    variant="outline"
                    disabled={isProcessing}
                    className="w-full"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Clear All
                  </Button>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Valid Texts:</span>
                    <span className="font-medium">{validTextCount}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Embeddings:</span>
                    <span className="font-medium">{embeddings.length}</span>
                  </div>
                </div>

                {isProcessing && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress:</span>
                      <span>{progress.current}/{progress.total}</span>
                    </div>
                    <Progress value={(progress.current / progress.total) * 100} />
                    <p className="text-xs text-slate-600 dark:text-slate-400">
                      {progress.message}
                    </p>
                  </div>
                )}

                <Separator />

                <div className="flex flex-col space-y-2">
                  <Button
                    onClick={() => setShowExportDialog(true)}
                    variant="outline"
                    disabled={!hasEmbeddings}
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </Button>
                  
                  <Button
                    onClick={() => setShowAdvancedSettings(true)}
                    variant="outline"
                    className="w-full"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Advanced Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="input" className="flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Input</span>
                </TabsTrigger>
                <TabsTrigger value="similarity" disabled={!hasEmbeddings}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  <span>Analysis</span>
                </TabsTrigger>
                <TabsTrigger value="visualization" disabled={!hasEmbeddings}>
                  <Brain className="h-4 w-4 mr-2" />
                  <span>3D View</span>
                </TabsTrigger>
                <TabsTrigger value="search" disabled={!hasEmbeddings}>
                  <Search className="h-4 w-4 mr-2" />
                  <span>Search</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="input" className="mt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Text Inputs</h3>
                    <Button onClick={addTextInput} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Text
                    </Button>
                  </div>
                  
                  <div className="grid gap-4">
                    {textInputs.map((input, index) => (
                      <TextInputCard
                        key={input.id}
                        id={input.id}
                        text={input.text}
                        label={input.label}
                        onTextChange={updateTextInput}
                        onLabelChange={updateTextLabel}
                        onRemove={removeTextInput}
                        onFileContent={handleFileContent}
                        canRemove={textInputs.length > 1}
                        isProcessing={isProcessing}
                      />
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="similarity" className="mt-6">
                <div className="grid gap-6">
                  <SimilarityStats 
                    matrix={similarityMatrix} 
                    labels={textInputs.map(input => input.label)} 
                  />
                  <SimilarityMatrix 
                    matrix={similarityMatrix} 
                    labels={textInputs.map(input => input.label)} 
                  />
                  <SimilarityPairs 
                    matrix={similarityMatrix} 
                    labels={textInputs.map(input => input.label)} 
                  />
                </div>
              </TabsContent>

              <TabsContent value="visualization" className="mt-6">
                <Embedding3DVisualization
                  embeddings={embeddings}
                  labels={textInputs.map(input => input.label)}
                />
              </TabsContent>

              <TabsContent value="search" className="mt-6">
                <SearchInterface
                  embeddings={embeddings}
                  labels={textInputs.map(input => input.label)}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Dialogs */}
        {showExportDialog && (
          <ExportDialog
            embeddings={embeddings}
            labels={textInputs.map(input => input.label)}
            onClose={() => setShowExportDialog(false)}
          />
        )}

        {showAdvancedSettings && (
          <AdvancedSettings
            onClose={() => setShowAdvancedSettings(false)}
          />
        )}
      </div>
    </div>
  )
}
