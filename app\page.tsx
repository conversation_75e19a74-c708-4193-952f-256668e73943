"use client"

import { useState, useEffect, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { ThemeToggle } from "@/components/theme-toggle"
import { Plus, Brain } from "lucide-react"
import { generateEmbedding, calculateSimilarityMatrix, type EmbeddingResult } from "@/lib/embedding"
import { SemanticSearchEngine } from "@/lib/search-engine"
import { EmbeddingCache, type ExportData } from "@/lib/export-utils"
import { PythonRequirementsNotice } from "@/components/python-requirements-notice"

interface TextInput {
  id: string
  text: string
  label: string
}

interface ProcessedText extends TextInput {
  embedding?: EmbeddingResult
}

export default function EmbeddingVisualizerPage() {
  const [textInputs, setTextInputs] = useState<ProcessedText[]>([{ id: "1", text: "", label: "Text 1" }])
  const [embeddings, setEmbeddings] = useState<EmbeddingResult[]>([])
  const [similarityMatrix, setSimilarityMatrix] = useState<number[][]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [modelLoaded, setModelLoaded] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentProcessing, setCurrentProcessing] = useState<string>("")
  const [apiStatus, setApiStatus] = useState<string>("Checking...")
  const [showRequirements, setShowRequirements] = useState(false)

  const searchEngineRef = useRef<SemanticSearchEngine>(new SemanticSearchEngine())

  useEffect(() => {
    const checkModelAvailability = async () => {
      try {
        console.log("[v0] Testing API availability...")

        // Test Python environment
        try {
          const pythonTest = await fetch("/api/test-python")
          if (pythonTest.ok) {
            const pythonResult = await pythonTest.json()
            console.log("[v0] Python test result:", pythonResult)
            if (pythonResult.status !== "success") {
              setShowRequirements(true)
            }
          }
        } catch (pythonError) {
          console.error("[v0] Python test failed:", pythonError)
          setShowRequirements(true)
        }

        // Test GET endpoint
        const getResponse = await fetch("/api/embeddings")
        if (getResponse.ok) {
          const getResult = await getResponse.json()
          console.log("[v0] GET endpoint working:", getResult)
        }

        // Test POST endpoint
        const postResponse = await fetch("/api/embeddings", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ text: "test connection" }),
        })

        if (postResponse.ok) {
          const result = await postResponse.json()
          console.log("[v0] POST endpoint working:", result)
          setModelLoaded(true)
          setApiStatus(result.fallback ? "Demo Mode" : "BGE Model Ready")
        } else {
          console.error("[v0] POST endpoint failed:", postResponse.status)
          setApiStatus(`API Error: ${postResponse.status}`)
        }
      } catch (error) {
        console.error("[v0] API connection failed:", error)
        setApiStatus("Connection Failed")
      }
    }
    checkModelAvailability()
  }, [])

  useEffect(() => {
    if (embeddings.length > 0) {
      const labels = textInputs.filter((input) => input.embedding).map((input) => input.label)
      const ids = textInputs.filter((input) => input.embedding).map((input) => input.id)
      searchEngineRef.current.updateCorpus(embeddings, labels, ids)
    }
  }, [embeddings, textInputs])

  const addTextInput = () => {
    const newId = Date.now().toString()
    const newLabel = `Text ${textInputs.length + 1}`
    setTextInputs([...textInputs, { id: newId, text: "", label: newLabel }])
  }

  const removeTextInput = (id: string) => {
    if (textInputs.length > 1) {
      setTextInputs(textInputs.filter((input) => input.id !== id))
      // Remove corresponding embedding if exists
      setEmbeddings((prev) => prev.filter((_, index) => textInputs[index]?.id !== id))
    }
  }

  const updateTextInput = (id: string, text: string) => {
    setTextInputs(textInputs.map((input) => (input.id === id ? { ...input, text } : input)))
  }

  const updateTextLabel = (id: string, label: string) => {
    setTextInputs(textInputs.map((input) => (input.id === id ? { ...input, label } : input)))
  }

  const handleFileContent = (content: string, filename: string) => {
    const newId = Date.now().toString()
    const label = filename.replace(/\.[^/.]+$/, "") // Remove file extension
    setTextInputs([...textInputs, { id: newId, text: content, label }])
  }

  const clearAllInputs = () => {
    setTextInputs([{ id: "1", text: "", label: "Text 1" }])
    setEmbeddings([])
    setSimilarityMatrix([])
    searchEngineRef.current.updateCorpus([], [], [])
  }

  const processEmbeddings = async () => {
    const validInputs = textInputs.filter((input) => input.text.trim())
    if (validInputs.length === 0) return

    setIsProcessing(true)
    setProgress(0)

    try {
      const results: EmbeddingResult[] = []

      for (let i = 0; i < validInputs.length; i++) {
        setCurrentProcessing(validInputs[i].label)

        const cachedEmbedding = EmbeddingCache.getFromCache(validInputs[i].text)
        let result: EmbeddingResult

        if (cachedEmbedding) {
          result = cachedEmbedding
        } else {
          result = await generateEmbedding(validInputs[i].text)
          EmbeddingCache.saveToCache(validInputs[i].text, result)
        }

        results.push(result)
        setProgress(((i + 1) / validInputs.length) * 100)

        // Update the specific input with embedding result
        setTextInputs((prev) =>
          prev.map((input) => (input.id === validInputs[i].id ? { ...input, embedding: result } : input)),
        )
      }

      setEmbeddings(results)

      if (results.length > 1) {
        const matrix = calculateSimilarityMatrix(results)
        setSimilarityMatrix(matrix)
      }
    } catch (error) {
      console.error("Processing failed:", error)
    } finally {
      setIsProcessing(false)
      setProgress(0)
      setCurrentProcessing("")
    }
  }

  const handleImportData = (importedData: ExportData) => {
    try {
      // Clear existing data
      setTextInputs([])
      setEmbeddings([])
      setSimilarityMatrix([])

      // Import embeddings and labels
      if (importedData.embeddings && importedData.labels) {
        const newTextInputs: ProcessedText[] = importedData.embeddings.map((embedding, index) => ({
          id: importedData.ids?.[index] || `imported-${index}`,
          text: embedding.text,
          label: importedData.labels[index] || `Imported Text ${index + 1}`,
          embedding,
        }))

        setTextInputs(newTextInputs)
        setEmbeddings(importedData.embeddings)

        if (importedData.similarityMatrix) {
          setSimilarityMatrix(importedData.similarityMatrix)
        }

        // Update search engine
        searchEngineRef.current.updateCorpus(
          importedData.embeddings,
          importedData.labels,
          importedData.ids || importedData.embeddings.map((_, i) => `imported-${i}`),
        )
      }
    } catch (error) {
      console.error("Failed to import data:", error)
      alert("Failed to import data. Please check the file format.")
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Brain className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-foreground">BGE Embedding Visualizer</h1>
                <p className="text-sm text-muted-foreground">Advanced semantic analysis with BAAI/bge-small-en-v1.5</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={modelLoaded ? "default" : "secondary"}>{apiStatus}</Badge>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <PythonRequirementsNotice show={showRequirements} />

        <Tabs defaultValue="input" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="input" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Input & Processing
            </TabsTrigger>
            <TabsTrigger value="similarity" className="flex items-center gap-2">
              Similarity Matrix
            </TabsTrigger>
            {/* Additional TabsTrigger can be added here if needed */}
          </TabsList>
          {/* Tab content can be added here if needed */}
        </Tabs>
      </div>
    </div>
  )
}
