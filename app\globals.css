@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.35 0.02 258);
  --card: oklch(0.98 0.01 195);
  --card-foreground: oklch(0.25 0.05 195);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.35 0.02 258);
  --primary: oklch(0.25 0.05 195);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.65 0.15 160);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.99 0.005 210);
  --muted-foreground: oklch(0.35 0.02 258);
  --accent: oklch(0.65 0.15 160);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.98 0.01 195);
  --input: oklch(0.97 0.01 210);
  --ring: oklch(0.65 0.15 160 / 0.5);
  --chart-1: oklch(0.55 0.15 160);
  --chart-2: oklch(0.25 0.05 195);
  --chart-3: oklch(0.65 0.15 160);
  --chart-4: oklch(0.35 0.02 258);
  --chart-5: oklch(0.98 0.01 195);
  --radius: 0.5rem;
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.35 0.02 258);
  --sidebar-primary: oklch(0.25 0.05 195);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.65 0.15 160);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.98 0.01 195);
  --sidebar-ring: oklch(0.65 0.15 160 / 0.5);
}

.dark {
  --background: oklch(0.08 0.01 258);
  --foreground: oklch(0.95 0.005 210);
  --card: oklch(0.12 0.02 258);
  --card-foreground: oklch(0.95 0.005 210);
  --popover: oklch(0.08 0.01 258);
  --popover-foreground: oklch(0.95 0.005 210);
  --primary: oklch(0.75 0.12 195);
  --primary-foreground: oklch(0.08 0.01 258);
  --secondary: oklch(0.55 0.15 160);
  --secondary-foreground: oklch(0.08 0.01 258);
  --muted: oklch(0.15 0.02 258);
  --muted-foreground: oklch(0.65 0.02 258);
  --accent: oklch(0.55 0.15 160);
  --accent-foreground: oklch(0.08 0.01 258);
  --destructive: oklch(0.55 0.2 25);
  --destructive-foreground: oklch(0.95 0.005 210);
  --border: oklch(0.15 0.02 258);
  --input: oklch(0.15 0.02 258);
  --ring: oklch(0.55 0.15 160 / 0.5);
  --chart-1: oklch(0.6 0.18 160);
  --chart-2: oklch(0.75 0.12 195);
  --chart-3: oklch(0.55 0.15 160);
  --chart-4: oklch(0.65 0.02 258);
  --chart-5: oklch(0.35 0.05 195);
  --sidebar: oklch(0.08 0.01 258);
  --sidebar-foreground: oklch(0.95 0.005 210);
  --sidebar-primary: oklch(0.75 0.12 195);
  --sidebar-primary-foreground: oklch(0.08 0.01 258);
  --sidebar-accent: oklch(0.55 0.15 160);
  --sidebar-accent-foreground: oklch(0.08 0.01 258);
  --sidebar-border: oklch(0.15 0.02 258);
  --sidebar-ring: oklch(0.55 0.15 160 / 0.5);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
