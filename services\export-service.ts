// Professional Export Service with Multiple Format Support

import type { 
  EmbeddingResult, 
  ExportData, 
  ExportFormat,
  SearchQuery 
} from '@/types'
import { EXPORT_CONFIG, APP_CONFIG } from '@/constants'

export class ExportService {
  /**
   * Export embeddings data in specified format
   */
  async exportEmbeddings(
    embeddings: EmbeddingResult[],
    labels: string[],
    format: ExportFormat,
    options: {
      includeMetadata?: boolean
      includeSimilarityMatrix?: boolean
      filename?: string
    } = {}
  ): Promise<{ data: string | Blob; filename: string; mimeType: string }> {
    const {
      includeMetadata = true,
      includeSimilarityMatrix = false,
      filename = `embeddings_${new Date().toISOString().split('T')[0]}`,
    } = options

    // Prepare export data
    const exportData: ExportData = {
      texts: embeddings.map(e => e.text),
      labels,
      embeddings: embeddings.map(e => e.embedding),
      similarityMatrix: includeSimilarityMatrix 
        ? this.calculateSimilarityMatrix(embeddings)
        : [],
      metadata: includeMetadata ? {
        model: embeddings[0]?.model || 'unknown',
        dimension: embeddings[0]?.dimension || 0,
        timestamp: new Date().toISOString(),
        totalTexts: embeddings.length,
      } : {} as any,
    }

    switch (format) {
      case 'json':
        return this.exportAsJSON(exportData, filename)
      case 'csv':
        return this.exportAsCSV(exportData, filename)
      case 'xlsx':
        return this.exportAsXLSX(exportData, filename)
      default:
        throw new Error(`Unsupported export format: ${format}`)
    }
  }

  /**
   * Export search results
   */
  async exportSearchResults(
    searchQueries: SearchQuery[],
    format: ExportFormat,
    filename = `search_results_${new Date().toISOString().split('T')[0]}`
  ): Promise<{ data: string | Blob; filename: string; mimeType: string }> {
    const exportData = {
      searches: searchQueries.map(query => ({
        id: query.id,
        query: query.query,
        timestamp: query.timestamp,
        processingTime: query.processingTime,
        results: query.results.map(result => ({
          id: result.id,
          text: result.text,
          label: result.label,
          similarity: result.similarity,
          rank: result.rank,
          highlights: result.highlights,
        })),
      })),
      metadata: {
        totalSearches: searchQueries.length,
        timestamp: new Date().toISOString(),
        exportedBy: APP_CONFIG.name,
      },
    }

    switch (format) {
      case 'json':
        return {
          data: JSON.stringify(exportData, null, 2),
          filename: `${filename}.json`,
          mimeType: 'application/json',
        }
      case 'csv':
        return this.exportSearchResultsAsCSV(searchQueries, filename)
      default:
        throw new Error(`Unsupported export format for search results: ${format}`)
    }
  }

  /**
   * Import embeddings data from file
   */
  async importEmbeddings(
    file: File
  ): Promise<{
    embeddings: EmbeddingResult[]
    labels: string[]
    metadata?: any
  }> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    
    if (file.size > EXPORT_CONFIG.maxFileSize) {
      throw new Error(`File size exceeds maximum of ${EXPORT_CONFIG.maxFileSize / (1024 * 1024)}MB`)
    }

    switch (fileExtension) {
      case 'json':
        return this.importFromJSON(file)
      case 'csv':
        return this.importFromCSV(file)
      default:
        throw new Error(`Unsupported import format: ${fileExtension}`)
    }
  }

  private async exportAsJSON(
    data: ExportData, 
    filename: string
  ): Promise<{ data: string; filename: string; mimeType: string }> {
    const jsonData = JSON.stringify(data, null, 2)
    
    return {
      data: jsonData,
      filename: `${filename}.json`,
      mimeType: 'application/json',
    }
  }

  private async exportAsCSV(
    data: ExportData, 
    filename: string
  ): Promise<{ data: string; filename: string; mimeType: string }> {
    const headers = ['text', 'label', ...Array.from({ length: data.embeddings[0]?.length || 0 }, (_, i) => `dim_${i}`)]
    
    const rows = data.texts.map((text, index) => [
      `"${text.replace(/"/g, '""')}"`, // Escape quotes in text
      `"${data.labels[index]?.replace(/"/g, '""') || ''}"`,
      ...data.embeddings[index].map(val => val.toString()),
    ])

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')

    return {
      data: csvContent,
      filename: `${filename}.csv`,
      mimeType: 'text/csv',
    }
  }

  private async exportAsXLSX(
    data: ExportData, 
    filename: string
  ): Promise<{ data: Blob; filename: string; mimeType: string }> {
    // For XLSX export, we'll create a simple XML-based Excel file
    // In a production environment, you'd use a library like xlsx or exceljs
    
    const worksheetData = [
      ['Text', 'Label', 'Embedding Dimensions'],
      ...data.texts.map((text, index) => [
        text,
        data.labels[index] || '',
        data.embeddings[index].join(';'), // Join dimensions with semicolon
      ]),
    ]

    // Create a simple XML structure for Excel
    const xmlContent = this.createExcelXML(worksheetData)
    const blob = new Blob([xmlContent], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })

    return {
      data: blob,
      filename: `${filename}.xlsx`,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }
  }

  private async exportSearchResultsAsCSV(
    searchQueries: SearchQuery[],
    filename: string
  ): Promise<{ data: string; filename: string; mimeType: string }> {
    const headers = [
      'search_id',
      'query',
      'timestamp',
      'processing_time',
      'result_rank',
      'result_id',
      'result_text',
      'result_label',
      'similarity',
      'highlights',
    ]

    const rows: string[] = []
    
    searchQueries.forEach(search => {
      search.results.forEach(result => {
        rows.push([
          search.id,
          `"${search.query.replace(/"/g, '""')}"`,
          search.timestamp.toISOString(),
          search.processingTime.toString(),
          result.rank.toString(),
          result.id,
          `"${result.text.replace(/"/g, '""')}"`,
          `"${result.label.replace(/"/g, '""')}"`,
          result.similarity.toString(),
          `"${(result.highlights || []).join('; ').replace(/"/g, '""')}"`,
        ].join(','))
      })
    })

    const csvContent = [headers.join(','), ...rows].join('\n')

    return {
      data: csvContent,
      filename: `${filename}.csv`,
      mimeType: 'text/csv',
    }
  }

  private async importFromJSON(file: File): Promise<{
    embeddings: EmbeddingResult[]
    labels: string[]
    metadata?: any
  }> {
    const text = await file.text()
    const data = JSON.parse(text) as ExportData

    if (!data.texts || !data.embeddings) {
      throw new Error('Invalid JSON format: missing required fields')
    }

    const embeddings: EmbeddingResult[] = data.texts.map((text, index) => ({
      text,
      embedding: data.embeddings[index],
      processingTime: 0,
      dimension: data.embeddings[index].length,
      model: data.metadata?.model || 'imported',
    }))

    return {
      embeddings,
      labels: data.labels || data.texts.map((_, i) => `Text ${i + 1}`),
      metadata: data.metadata,
    }
  }

  private async importFromCSV(file: File): Promise<{
    embeddings: EmbeddingResult[]
    labels: string[]
    metadata?: any
  }> {
    const text = await file.text()
    const lines = text.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      throw new Error('CSV file must have at least a header and one data row')
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const embeddings: EmbeddingResult[] = []
    const labels: string[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i])
      
      if (values.length < 3) {
        continue // Skip invalid rows
      }

      const text = values[0]
      const label = values[1]
      const embeddingValues = values.slice(2).map(v => parseFloat(v)).filter(v => !isNaN(v))

      if (embeddingValues.length === 0) {
        continue // Skip rows without valid embeddings
      }

      embeddings.push({
        text,
        embedding: embeddingValues,
        processingTime: 0,
        dimension: embeddingValues.length,
        model: 'imported',
      })

      labels.push(label)
    }

    return {
      embeddings,
      labels,
      metadata: {
        importedFrom: file.name,
        importedAt: new Date().toISOString(),
      },
    }
  }

  private parseCSVLine(line: string): string[] {
    const values: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"'
          i++ // Skip next quote
        } else {
          inQuotes = !inQuotes
        }
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    values.push(current.trim())
    return values
  }

  private calculateSimilarityMatrix(embeddings: EmbeddingResult[]): number[][] {
    const matrix: number[][] = []
    
    for (let i = 0; i < embeddings.length; i++) {
      matrix[i] = []
      for (let j = 0; j < embeddings.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0
        } else {
          matrix[i][j] = this.cosineSimilarity(
            embeddings[i].embedding,
            embeddings[j].embedding
          )
        }
      }
    }

    return matrix
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    let dotProduct = 0
    let normA = 0
    let normB = 0

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i]
      normA += a[i] * a[i]
      normB += b[i] * b[i]
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  private createExcelXML(data: any[][]): string {
    const rows = data.map(row => 
      `<Row>${row.map(cell => `<Cell><Data ss:Type="String">${this.escapeXML(String(cell))}</Data></Cell>`).join('')}</Row>`
    ).join('')

    return `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <Worksheet ss:Name="Embeddings">
  <Table>
   ${rows}
  </Table>
 </Worksheet>
</Workbook>`
  }

  private escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }
}

// Singleton instance
export const exportService = new ExportService()
