export interface EmbeddingResult {
  text: string
  embedding: number[]
  chunks?: string[]
  processingTime: number
}

export interface SimilarityResult {
  text1: string
  text2: string
  similarity: number
}

// Generate embeddings using server-side API
export async function generateEmbedding(text: string): Promise<EmbeddingResult> {
  try {
    const response = await fetch("/api/embeddings", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ text }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error("Failed to generate embedding:", error)
    throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

// Generate multiple embeddings in batch
export async function generateBatchEmbeddings(texts: string[]): Promise<EmbeddingResult[]> {
  try {
    const response = await fetch("/api/embeddings/batch", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ texts }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    return result.results
  } catch (error) {
    console.error("Failed to generate batch embeddings:", error)
    throw new Error(`Failed to generate batch embeddings: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

// Calculate cosine similarity between two embeddings
export function calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
  if (embedding1.length !== embedding2.length) {
    throw new Error("Embeddings must have the same dimension")
  }

  const dotProduct = embedding1.reduce((sum, a, i) => sum + a * embedding2[i], 0)
  const magnitude1 = Math.sqrt(embedding1.reduce((sum, a) => sum + a * a, 0))
  const magnitude2 = Math.sqrt(embedding2.reduce((sum, a) => sum + a * a, 0))

  return dotProduct / (magnitude1 * magnitude2)
}

// Calculate similarity matrix for multiple texts
export function calculateSimilarityMatrix(embeddings: EmbeddingResult[]): number[][] {
  const matrix: number[][] = []

  for (let i = 0; i < embeddings.length; i++) {
    matrix[i] = []
    for (let j = 0; j < embeddings.length; j++) {
      if (i === j) {
        matrix[i][j] = 1.0
      } else {
        matrix[i][j] = calculateCosineSimilarity(embeddings[i].embedding, embeddings[j].embedding)
      }
    }
  }

  return matrix
}

// Find most similar texts
export function findMostSimilar(
  queryEmbedding: EmbeddingResult,
  candidateEmbeddings: EmbeddingResult[],
  topK = 5,
): Array<{ result: EmbeddingResult; similarity: number }> {
  const similarities = candidateEmbeddings.map((candidate) => ({
    result: candidate,
    similarity: calculateCosineSimilarity(queryEmbedding.embedding, candidate.embedding),
  }))

  return similarities.sort((a, b) => b.similarity - a.similarity).slice(0, topK)
}
