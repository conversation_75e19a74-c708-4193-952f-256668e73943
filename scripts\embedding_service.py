import json
import sys
import time
from transformers import AutoTokenizer, AutoModel
import torch
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BGEEmbeddingService:
    def __init__(self, model_name="BAAI/bge-small-en-v1.5"):
        """Initialize the BGE embedding service"""
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
    def load_model(self):
        """Load the BGE model and tokenizer"""
        try:
            logger.info(f"Loading model: {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModel.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            logger.info("Model loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False
    
    def generate_embedding(self, text):
        """Generate embedding for a single text"""
        if not self.model or not self.tokenizer:
            if not self.load_model():
                raise Exception("Failed to load model")
        
        try:
            # Tokenize and encode
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, 
                                  max_length=512, padding=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Generate embeddings
            with torch.no_grad():
                outputs = self.model(**inputs)
                # Use mean pooling
                embeddings = outputs.last_hidden_state.mean(dim=1)
                # Normalize
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            
            return embeddings.cpu().numpy().flatten().tolist()
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise
    
    def generate_batch_embeddings(self, texts):
        """Generate embeddings for multiple texts"""
        embeddings = []
        for i, text in enumerate(texts):
            logger.info(f"Processing text {i+1}/{len(texts)}")
            embedding = self.generate_embedding(text)
            embeddings.append(embedding)
        return embeddings
    
    def calculate_similarity_matrix(self, embeddings):
        """Calculate cosine similarity matrix"""
        embeddings_array = np.array(embeddings)
        similarity_matrix = cosine_similarity(embeddings_array)
        return similarity_matrix.tolist()
    
    def reduce_dimensions(self, embeddings, method="pca", n_components=3):
        """Reduce embeddings to 3D for visualization"""
        embeddings_array = np.array(embeddings)
        
        if method.lower() == "pca":
            reducer = PCA(n_components=n_components)
            reduced = reducer.fit_transform(embeddings_array)
            variance_explained = reducer.explained_variance_ratio_.tolist()
            return {
                "coordinates": reduced.tolist(),
                "variance_explained": variance_explained,
                "method": "PCA"
            }
        else:
            # For now, just return PCA. t-SNE can be added later
            return self.reduce_dimensions(embeddings, "pca", n_components)

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) < 2:
        print(json.dumps({"error": "No command provided"}))
        return
    
    command = sys.argv[1]
    service = BGEEmbeddingService()
    
    try:
        if command == "status":
            # Check if service is ready
            try:
                service.load_model()
                result = {
                    "status": "ready",
                    "model": service.model_name,
                    "device": str(service.device)
                }
                print(json.dumps(result))
            except Exception as e:
                result = {
                    "status": "error",
                    "error": str(e)
                }
                print(json.dumps(result))
                sys.exit(1)

        elif command == "single":
            # Generate single embedding
            text = sys.argv[2] if len(sys.argv) > 2 else ""
            embedding = service.generate_embedding(text)
            result = {
                "text": text,
                "embedding": embedding,
                "dimension": len(embedding),
                "model": service.model_name
            }
            print(json.dumps(result))
            
        elif command == "batch":
            # Generate batch embeddings
            texts_json = sys.argv[2] if len(sys.argv) > 2 else "[]"
            texts = json.loads(texts_json)
            
            embeddings = service.generate_batch_embeddings(texts)
            similarity_matrix = service.calculate_similarity_matrix(embeddings)
            reduced_3d = service.reduce_dimensions(embeddings)
            
            result = {
                "texts": texts,
                "embeddings": embeddings,
                "similarity_matrix": similarity_matrix,
                "visualization_3d": reduced_3d,
                "dimension": len(embeddings[0]) if embeddings else 0,
                "model": service.model_name,
                "count": len(texts)
            }
            print(json.dumps(result))
            
        elif command == "test":
            # Test model loading
            success = service.load_model()
            result = {
                "model_loaded": success,
                "model": service.model_name,
                "device": str(service.device)
            }
            print(json.dumps(result))
            print(json.dumps(result))
            
        else:
            print(json.dumps({"error": f"Unknown command: {command}"}))
            
    except Exception as e:
        print(json.dumps({"error": str(e)}))

if __name__ == "__main__":
    main()
