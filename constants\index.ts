// Application Constants

export const APP_CONFIG = {
  name: 'BGE Embedding Visualizer',
  version: '2.0.0',
  description: 'Advanced semantic analysis with BGE/bge-small-en-v1.5 embeddings',
  author: 'Embedding Tools Team',
} as const

export const API_ENDPOINTS = {
  embeddings: '/api/embeddings',
  batch: '/api/embeddings/batch',
  status: '/api/status',
  health: '/api/health',
} as const

export const EMBEDDING_CONFIG = {
  models: {
    'bge-small-en-v1.5': {
      name: 'B<PERSON> Small EN v1.5',
      dimension: 384,
      maxTokens: 512,
      description: 'High-quality English embeddings optimized for semantic similarity',
    },
    mock: {
      name: 'Mock Embeddings (Demo)',
      dimension: 384,
      maxTokens: 512,
      description: 'Mock embeddings for demonstration purposes',
    },
  },
  defaultModel: 'bge-small-en-v1.5',
  batchSize: 32,
  maxTextLength: 8192,
  timeout: 30000,
} as const

export const VISUALIZATION_CONFIG = {
  defaultSettings: {
    reductionMethod: 'pca' as const,
    numClusters: 3,
    showLabels: true,
    autoRotate: false,
    pointSize: 8,
    colorScheme: 'default' as const,
  },
  camera: {
    defaultPosition: { x: 0, y: 0, z: 5 },
    defaultRotation: { x: 0, y: 0 },
    defaultZoom: 1,
    minZoom: 0.1,
    maxZoom: 10,
  },
  animation: {
    duration: 1000,
    easing: 'ease-in-out',
  },
  colors: {
    default: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'],
    cluster: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'],
    similarity: ['#1e3a8a', '#3b82f6', '#60a5fa', '#93c5fd', '#dbeafe'],
  },
  maxPoints: 1000,
} as const

export const SEARCH_CONFIG = {
  defaultTopK: 10,
  minSimilarity: 0.1,
  maxSimilarity: 1.0,
  highlightThreshold: 0.7,
  cacheSize: 100,
} as const

export const EXPORT_CONFIG = {
  formats: ['json', 'csv', 'xlsx'] as const,
  maxFileSize: 50 * 1024 * 1024, // 50MB
  compression: true,
  includeMetadata: true,
} as const

export const UI_CONFIG = {
  theme: {
    borderRadius: '0.5rem',
    colors: {
      primary: 'hsl(221.2 83.2% 53.3%)',
      secondary: 'hsl(210 40% 98%)',
      accent: 'hsl(210 40% 96%)',
      muted: 'hsl(210 40% 96%)',
      destructive: 'hsl(0 84.2% 60.2%)',
    },
  },
  animations: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
} as const

export const PROCESSING_CONFIG = {
  maxConcurrentRequests: 5,
  retryAttempts: 3,
  retryDelay: 1000,
  progressUpdateInterval: 100,
} as const

export const CACHE_CONFIG = {
  embeddings: {
    key: 'bge-embeddings-cache',
    maxSize: 1000,
    ttl: 24 * 60 * 60 * 1000, // 24 hours
  },
  search: {
    key: 'search-results-cache',
    maxSize: 100,
    ttl: 60 * 60 * 1000, // 1 hour
  },
} as const

export const ERROR_MESSAGES = {
  network: 'Network error. Please check your connection and try again.',
  timeout: 'Request timed out. Please try again.',
  invalidInput: 'Invalid input provided. Please check your data.',
  serverError: 'Server error occurred. Please try again later.',
  pythonNotAvailable: 'Python environment not available. Using mock embeddings.',
  embeddingFailed: 'Failed to generate embeddings. Please try again.',
  exportFailed: 'Failed to export data. Please try again.',
  importFailed: 'Failed to import data. Please check the file format.',
} as const

export const SUCCESS_MESSAGES = {
  embeddingGenerated: 'Embeddings generated successfully',
  dataExported: 'Data exported successfully',
  dataImported: 'Data imported successfully',
  settingsSaved: 'Settings saved successfully',
} as const

export const VALIDATION_RULES = {
  text: {
    minLength: 1,
    maxLength: 8192,
  },
  label: {
    minLength: 1,
    maxLength: 100,
  },
  batch: {
    minSize: 1,
    maxSize: 100,
  },
} as const

export const KEYBOARD_SHORTCUTS = {
  search: 'Ctrl+K',
  export: 'Ctrl+E',
  import: 'Ctrl+I',
  reset: 'Ctrl+R',
  help: 'F1',
} as const

export const FEATURE_FLAGS = {
  enableRealTimeProcessing: true,
  enableAdvancedVisualization: true,
  enableExport: true,
  enableImport: true,
  enableSearch: true,
  enableCaching: true,
  enableAnalytics: false,
} as const

// Python Integration Constants
export const PYTHON_CONFIG = {
  scriptPath: './scripts/embedding_service.py',
  timeout: 60000,
  encoding: 'utf8',
  requirements: [
    'transformers>=4.21.0',
    'torch>=1.12.0',
    'scikit-learn>=1.1.0',
    'numpy>=1.21.0',
  ],
  installCommand: 'pip install transformers torch scikit-learn numpy',
} as const

// Development Constants
export const DEV_CONFIG = {
  enableDebugMode: process.env.NODE_ENV === 'development',
  enableVerboseLogging: process.env.NODE_ENV === 'development',
  mockDataEnabled: true,
  performanceMonitoring: true,
} as const
