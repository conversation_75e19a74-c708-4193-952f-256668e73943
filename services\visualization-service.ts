// Professional Visualization Service with Advanced Dimensionality Reduction

import type { 
  EmbeddingR<PERSON>ult, 
  Point3D, 
  PCAResult, 
  TSNEResult, 
  ClusterResult,
  VisualizationSettings 
} from '@/types'
import { VISUALIZATION_CONFIG } from '@/constants'

export class VisualizationService {
  /**
   * Perform Principal Component Analysis (PCA)
   */
  performPCA(embeddings: number[][], targetDimensions = 3): PCAResult {
    const n = embeddings.length
    const d = embeddings[0].length

    if (n === 0 || d === 0) {
      throw new Error('Invalid embeddings data')
    }

    // Center the data
    const means = new Array(d).fill(0)
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < d; j++) {
        means[j] += embeddings[i][j]
      }
    }
    for (let j = 0; j < d; j++) {
      means[j] /= n
    }

    const centeredData = embeddings.map(row => 
      row.map((val, j) => val - means[j])
    )

    // Compute covariance matrix efficiently
    const covMatrix = this.computeCovarianceMatrix(centeredData)
    
    // Perform eigendecomposition
    const { eigenvalues, eigenvectors } = this.eigenDecomposition(
      covMatrix, 
      targetDimensions
    )

    // Project data onto principal components
    const reducedData = centeredData.map(row => {
      return eigenvectors
        .slice(0, targetDimensions)
        .map(eigenvector => 
          row.reduce((sum, val, i) => sum + val * eigenvector[i], 0)
        )
    })

    const totalVariance = eigenvalues.reduce((sum, val) => sum + val, 0)
    const explainedVariance = eigenvalues
      .slice(0, targetDimensions)
      .map(val => val / totalVariance)

    return {
      reducedData,
      explainedVariance,
      totalVariance,
      components: eigenvectors.slice(0, targetDimensions),
    }
  }

  /**
   * Perform t-SNE dimensionality reduction
   */
  performTSNE(
    embeddings: number[][], 
    targetDimensions = 3, 
    perplexity = 30,
    iterations = 1000
  ): TSNEResult {
    const n = embeddings.length
    
    if (n < 4) {
      throw new Error('t-SNE requires at least 4 data points')
    }

    // Initialize random positions
    const Y = Array.from({ length: n }, () => 
      Array.from({ length: targetDimensions }, () => 
        (Math.random() - 0.5) * 1e-4
      )
    )

    // Calculate pairwise distances in high-dimensional space
    const distances = this.calculatePairwiseDistances(embeddings)
    
    // Calculate P matrix (similarities in high-dimensional space)
    const P = this.calculatePMatrix(distances, perplexity)
    
    // Gradient descent optimization
    const learningRate = 200
    const momentum = 0.8
    let previousGradient = Array.from({ length: n }, () => 
      new Array(targetDimensions).fill(0)
    )

    for (let iter = 0; iter < iterations; iter++) {
      // Calculate Q matrix (similarities in low-dimensional space)
      const Q = this.calculateQMatrix(Y)
      
      // Calculate gradient
      const gradient = this.calculateTSNEGradient(P, Q, Y)
      
      // Update positions with momentum
      for (let i = 0; i < n; i++) {
        for (let j = 0; j < targetDimensions; j++) {
          const update = momentum * previousGradient[i][j] - 
                        learningRate * gradient[i][j]
          Y[i][j] += update
          previousGradient[i][j] = update
        }
      }
      
      // Adaptive learning rate
      if (iter === 100) {
        // Reduce learning rate after early exaggeration phase
        learningRate *= 0.5
      }
    }

    return {
      reducedData: Y,
      perplexity,
      iterations,
    }
  }

  /**
   * Perform K-means clustering
   */
  performKMeansClustering(
    embeddings: number[][], 
    numClusters: number,
    maxIterations = 100
  ): ClusterResult {
    const n = embeddings.length
    const d = embeddings[0].length

    if (numClusters >= n) {
      throw new Error('Number of clusters must be less than number of data points')
    }

    // Initialize centroids randomly
    let centroids = this.initializeCentroids(embeddings, numClusters)
    let labels = new Array(n).fill(0)
    let previousLabels = new Array(n).fill(-1)

    for (let iter = 0; iter < maxIterations; iter++) {
      // Assign points to nearest centroid
      for (let i = 0; i < n; i++) {
        let minDistance = Infinity
        let closestCentroid = 0

        for (let k = 0; k < numClusters; k++) {
          const distance = this.euclideanDistance(embeddings[i], centroids[k])
          if (distance < minDistance) {
            minDistance = distance
            closestCentroid = k
          }
        }

        labels[i] = closestCentroid
      }

      // Check for convergence
      if (this.arraysEqual(labels, previousLabels)) {
        break
      }
      previousLabels = [...labels]

      // Update centroids
      const newCentroids = Array.from({ length: numClusters }, () => 
        new Array(d).fill(0)
      )
      const counts = new Array(numClusters).fill(0)

      for (let i = 0; i < n; i++) {
        const cluster = labels[i]
        counts[cluster]++
        for (let j = 0; j < d; j++) {
          newCentroids[cluster][j] += embeddings[i][j]
        }
      }

      for (let k = 0; k < numClusters; k++) {
        if (counts[k] > 0) {
          for (let j = 0; j < d; j++) {
            newCentroids[k][j] /= counts[k]
          }
        }
      }

      centroids = newCentroids
    }

    // Calculate inertia (within-cluster sum of squares)
    let inertia = 0
    for (let i = 0; i < n; i++) {
      const cluster = labels[i]
      inertia += Math.pow(this.euclideanDistance(embeddings[i], centroids[cluster]), 2)
    }

    return {
      labels,
      centroids,
      inertia,
      numClusters,
    }
  }

  /**
   * Generate 3D points for visualization
   */
  generateVisualizationPoints(
    embeddings: EmbeddingResult[],
    labels: string[],
    settings: VisualizationSettings
  ): Point3D[] {
    if (embeddings.length === 0) return []

    const embeddingVectors = embeddings.map(e => e.embedding)
    
    // Perform dimensionality reduction
    let reducedData: number[][]
    if (settings.reductionMethod === 'pca') {
      const pcaResult = this.performPCA(embeddingVectors, 3)
      reducedData = pcaResult.reducedData
    } else {
      const tsneResult = this.performTSNE(embeddingVectors, 3)
      reducedData = tsneResult.reducedData
    }

    // Perform clustering if requested
    let clusterLabels: number[] | null = null
    if (settings.numClusters > 1) {
      const clusterResult = this.performKMeansClustering(
        embeddingVectors, 
        settings.numClusters
      )
      clusterLabels = clusterResult.labels
    }

    // Generate colors based on color scheme
    const colors = this.generateColors(
      embeddings.length, 
      settings.colorScheme,
      clusterLabels
    )

    // Create 3D points
    return reducedData.map((coords, index) => ({
      x: coords[0],
      y: coords[1],
      z: coords[2],
      label: labels[index] || `Point ${index + 1}`,
      color: colors[index],
      cluster: clusterLabels?.[index],
      originalIndex: index,
    }))
  }

  private computeCovarianceMatrix(centeredData: number[][]): number[][] {
    const n = centeredData.length
    const d = centeredData[0].length
    const covMatrix = Array.from({ length: d }, () => new Array(d).fill(0))

    for (let i = 0; i < d; i++) {
      for (let j = i; j < d; j++) {
        let sum = 0
        for (let k = 0; k < n; k++) {
          sum += centeredData[k][i] * centeredData[k][j]
        }
        covMatrix[i][j] = sum / (n - 1)
        covMatrix[j][i] = covMatrix[i][j]
      }
    }

    return covMatrix
  }

  private eigenDecomposition(
    matrix: number[][], 
    numComponents: number
  ): { eigenvalues: number[]; eigenvectors: number[][] } {
    const d = matrix.length
    const eigenvalues: number[] = []
    const eigenvectors: number[][] = []

    // Power iteration for dominant eigenvectors
    for (let comp = 0; comp < Math.min(numComponents, d); comp++) {
      let vector = Array.from({ length: d }, () => Math.random() - 0.5)
      
      // Power iteration
      for (let iter = 0; iter < 100; iter++) {
        const newVector = new Array(d).fill(0)

        // Matrix-vector multiplication
        for (let i = 0; i < d; i++) {
          for (let j = 0; j < d; j++) {
            newVector[i] += matrix[i][j] * vector[j]
          }
        }

        // Orthogonalize against previous eigenvectors
        for (const prevEigenvector of eigenvectors) {
          const dot = newVector.reduce((sum, val, i) => sum + val * prevEigenvector[i], 0)
          for (let i = 0; i < d; i++) {
            newVector[i] -= dot * prevEigenvector[i]
          }
        }

        // Normalize
        const norm = Math.sqrt(newVector.reduce((sum, val) => sum + val * val, 0))
        if (norm > 1e-10) {
          vector = newVector.map(val => val / norm)
        }
      }

      // Calculate eigenvalue
      const eigenvalue = vector.reduce((sum, val, i) => {
        const matrixRow = matrix[i].reduce((rowSum, matVal, j) => 
          rowSum + matVal * vector[j], 0
        )
        return sum + val * matrixRow
      }, 0)

      eigenvectors.push(vector)
      eigenvalues.push(Math.max(0, eigenvalue))
    }

    return { eigenvalues, eigenvectors }
  }

  private calculatePairwiseDistances(embeddings: number[][]): number[][] {
    const n = embeddings.length
    const distances = Array.from({ length: n }, () => new Array(n).fill(0))

    for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
        const dist = this.euclideanDistance(embeddings[i], embeddings[j])
        distances[i][j] = dist
        distances[j][i] = dist
      }
    }

    return distances
  }

  private calculatePMatrix(distances: number[][], perplexity: number): number[][] {
    const n = distances.length
    const P = Array.from({ length: n }, () => new Array(n).fill(0))

    for (let i = 0; i < n; i++) {
      // Binary search for sigma that gives desired perplexity
      let sigma = 1.0
      let minSigma = 0
      let maxSigma = Infinity

      for (let iter = 0; iter < 50; iter++) {
        const probabilities = new Array(n).fill(0)
        let sum = 0

        for (let j = 0; j < n; j++) {
          if (i !== j) {
            probabilities[j] = Math.exp(-distances[i][j] * distances[i][j] / (2 * sigma * sigma))
            sum += probabilities[j]
          }
        }

        if (sum === 0) {
          sigma *= 2
          continue
        }

        // Normalize
        for (let j = 0; j < n; j++) {
          probabilities[j] /= sum
        }

        // Calculate entropy
        let entropy = 0
        for (let j = 0; j < n; j++) {
          if (probabilities[j] > 1e-12) {
            entropy -= probabilities[j] * Math.log2(probabilities[j])
          }
        }

        const currentPerplexity = Math.pow(2, entropy)
        
        if (Math.abs(currentPerplexity - perplexity) < 1e-5) {
          for (let j = 0; j < n; j++) {
            P[i][j] = probabilities[j]
          }
          break
        }

        if (currentPerplexity > perplexity) {
          maxSigma = sigma
          sigma = (sigma + minSigma) / 2
        } else {
          minSigma = sigma
          sigma = maxSigma === Infinity ? sigma * 2 : (sigma + maxSigma) / 2
        }
      }
    }

    // Symmetrize
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        P[i][j] = (P[i][j] + P[j][i]) / (2 * n)
      }
    }

    return P
  }

  private calculateQMatrix(Y: number[][]): number[][] {
    const n = Y.length
    const Q = Array.from({ length: n }, () => new Array(n).fill(0))
    let sum = 0

    for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
        const dist = this.euclideanDistance(Y[i], Y[j])
        const q = 1 / (1 + dist * dist)
        Q[i][j] = q
        Q[j][i] = q
        sum += 2 * q
      }
    }

    // Normalize
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        Q[i][j] = Math.max(Q[i][j] / sum, 1e-12)
      }
    }

    return Q
  }

  private calculateTSNEGradient(P: number[][], Q: number[][], Y: number[][]): number[][] {
    const n = Y.length
    const d = Y[0].length
    const gradient = Array.from({ length: n }, () => new Array(d).fill(0))

    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        if (i !== j) {
          const diff = Y[i].map((val, k) => val - Y[j][k])
          const dist = this.euclideanDistance(Y[i], Y[j])
          const factor = 4 * (P[i][j] - Q[i][j]) / (1 + dist * dist)

          for (let k = 0; k < d; k++) {
            gradient[i][k] += factor * diff[k]
          }
        }
      }
    }

    return gradient
  }

  private initializeCentroids(embeddings: number[][], numClusters: number): number[][] {
    const n = embeddings.length
    const d = embeddings[0].length
    const centroids: number[][] = []

    // Use k-means++ initialization
    const chosen = new Set<number>()
    
    // Choose first centroid randomly
    const firstIndex = Math.floor(Math.random() * n)
    centroids.push([...embeddings[firstIndex]])
    chosen.add(firstIndex)

    // Choose remaining centroids
    for (let k = 1; k < numClusters; k++) {
      const distances = embeddings.map((embedding, i) => {
        if (chosen.has(i)) return 0
        
        let minDist = Infinity
        for (const centroid of centroids) {
          const dist = this.euclideanDistance(embedding, centroid)
          minDist = Math.min(minDist, dist)
        }
        return minDist * minDist
      })

      const totalDist = distances.reduce((sum, dist) => sum + dist, 0)
      let random = Math.random() * totalDist
      
      for (let i = 0; i < n; i++) {
        if (chosen.has(i)) continue
        random -= distances[i]
        if (random <= 0) {
          centroids.push([...embeddings[i]])
          chosen.add(i)
          break
        }
      }
    }

    return centroids
  }

  private euclideanDistance(a: number[], b: number[]): number {
    return Math.sqrt(
      a.reduce((sum, val, i) => sum + Math.pow(val - b[i], 2), 0)
    )
  }

  private arraysEqual(a: number[], b: number[]): boolean {
    return a.length === b.length && a.every((val, i) => val === b[i])
  }

  private generateColors(
    count: number, 
    scheme: string, 
    clusterLabels?: number[] | null
  ): string[] {
    const colors = VISUALIZATION_CONFIG.colors[scheme] || VISUALIZATION_CONFIG.colors.default
    
    if (scheme === 'cluster' && clusterLabels) {
      return clusterLabels.map(label => colors[label % colors.length])
    }
    
    return Array.from({ length: count }, (_, i) => colors[i % colors.length])
  }
}

// Singleton instance
export const visualizationService = new VisualizationService()
