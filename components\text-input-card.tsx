"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { X, Edit2, Check, FileText, Clock } from "lucide-react"
import { cn } from "@/lib/utils"

interface TextInputCardProps {
  id: string
  label: string
  text: string
  onTextChange: (text: string) => void
  onLabelChange: (label: string) => void
  onRemove: () => void
  canRemove: boolean
  isProcessed?: boolean
  processingTime?: number
  wordCount?: number
}

export function TextInputCard({
  id,
  label,
  text,
  onTextChange,
  onLabelChange,
  onRemove,
  canRemove,
  isProcessed = false,
  processingTime,
  wordCount,
}: TextInputCardProps) {
  const [isEditingLabel, setIsEditingLabel] = useState(false)
  const [tempLabel, setTempLabel] = useState(label)

  const handleLabelSave = () => {
    onLabelChange(tempLabel)
    setIsEditingLabel(false)
  }

  const handleLabelCancel = () => {
    setTempLabel(label)
    setIsEditingLabel(false)
  }

  const currentWordCount = text
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0).length

  return (
    <Card className={cn("transition-all duration-200", isProcessed && "ring-2 ring-primary/20")}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isEditingLabel ? (
              <div className="flex items-center gap-2">
                <Input
                  value={tempLabel}
                  onChange={(e) => setTempLabel(e.target.value)}
                  className="h-8 w-32"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") handleLabelSave()
                    if (e.key === "Escape") handleLabelCancel()
                  }}
                  autoFocus
                />
                <Button size="sm" variant="ghost" onClick={handleLabelSave}>
                  <Check className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <h3 className="font-medium text-sm">{label}</h3>
                <Button size="sm" variant="ghost" onClick={() => setIsEditingLabel(true)}>
                  <Edit2 className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            {isProcessed && (
              <Badge variant="secondary" className="text-xs">
                <Check className="h-3 w-3 mr-1" />
                Processed
              </Badge>
            )}
            {canRemove && (
              <Button variant="ghost" size="sm" onClick={onRemove}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <Textarea
          placeholder="Enter your text here or drag & drop a file..."
          value={text}
          onChange={(e) => onTextChange(e.target.value)}
          className="min-h-[120px] resize-none"
        />
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              <span>{currentWordCount} words</span>
            </div>
            {processingTime && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{processingTime}ms</span>
              </div>
            )}
          </div>
          <div className="text-xs">
            {text.length > 512 && (
              <Badge variant="outline" className="text-xs">
                Will be chunked
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
