"use client"

import type React from "react"

import { useEffect, useRef, useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>lider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Loader2, RotateCcw } from "lucide-react"
import { performPCA, performTSNE, performKMeansClustering, type PCAResult } from "@/lib/dimensionality-reduction"
import type { EmbeddingResult } from "@/lib/embedding"

interface Point3D {
  x: number
  y: number
  z: number
  label: string
  cluster: number
  originalIndex: number
}

interface Embedding3DVisualizationProps {
  embeddings: EmbeddingResult[]
  labels: string[]
  className?: string
}

export function Embedding3DVisualization({ embeddings, labels, className }: Embedding3DVisualizationProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [points3D, setPoints3D] = useState<Point3D[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [reductionMethod, setReductionMethod] = useState<"pca" | "tsne">("pca")
  const [numClusters, setNumClusters] = useState([3])
  const [showLabels, setShowLabels] = useState(true)
  const [autoRotate, setAutoRotate] = useState(false)
  const [pcaResult, setPcaResult] = useState<PCAResult | null>(null)

  // Camera controls
  const [camera, setCamera] = useState({
    x: 0,
    y: 0,
    z: 5,
    rotationX: 0,
    rotationY: 0,
    zoom: 1,
  })

  const clusterColors = [
    "#10b981", // emerald-500
    "#3b82f6", // blue-500
    "#f59e0b", // amber-500
    "#ef4444", // red-500
    "#8b5cf6", // violet-500
    "#06b6d4", // cyan-500
    "#84cc16", // lime-500
    "#f97316", // orange-500
  ]

  useEffect(() => {
    if (embeddings.length < 2) return

    const processEmbeddings = async () => {
      setIsProcessing(true)

      try {
        const embeddingVectors = embeddings.map((e) => e.embedding)
        let reducedData: number[][]
        let pcaInfo: PCAResult | null = null

        if (reductionMethod === "pca") {
          const result = performPCA(embeddingVectors, 3)
          reducedData = result.reducedData
          pcaInfo = result
          setPcaResult(result)
        } else {
          reducedData = performTSNE(embeddingVectors, 3)
          setPcaResult(null)
        }

        // Perform clustering
        const clusters = performKMeansClustering(reducedData, numClusters[0])

        // Create 3D points
        const newPoints: Point3D[] = reducedData.map((point, index) => ({
          x: point[0],
          y: point[1],
          z: point[2],
          label: labels[index] || `Text ${index + 1}`,
          cluster: clusters[index],
          originalIndex: index,
        }))

        setPoints3D(newPoints)
      } catch (error) {
        console.error("3D visualization processing failed:", error)
      } finally {
        setIsProcessing(false)
      }
    }

    processEmbeddings()
  }, [embeddings, labels, reductionMethod, numClusters])

  useEffect(() => {
    if (!canvasRef.current || points3D.length === 0) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const render = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Set canvas size
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

      const centerX = rect.width / 2
      const centerY = rect.height / 2
      const scale = Math.min(rect.width, rect.height) * 0.3 * camera.zoom

      // Auto-rotate if enabled
      if (autoRotate) {
        setCamera((prev) => ({
          ...prev,
          rotationY: prev.rotationY + 0.01,
        }))
      }

      // Transform and project points
      const projectedPoints = points3D.map((point) => {
        // Apply rotation
        const cosX = Math.cos(camera.rotationX)
        const sinX = Math.sin(camera.rotationX)
        const cosY = Math.cos(camera.rotationY)
        const sinY = Math.sin(camera.rotationY)

        // Rotate around Y axis
        const x = point.x * cosY - point.z * sinY
        let z = point.x * sinY + point.z * cosY
        let y = point.y

        // Rotate around X axis
        const newY = y * cosX - z * sinX
        z = y * sinX + z * cosX
        y = newY

        // Apply camera position
        z += camera.z

        // Project to 2D
        const perspective = 1000 / (1000 + z)
        const screenX = centerX + x * scale * perspective
        const screenY = centerY - y * scale * perspective

        return {
          ...point,
          screenX,
          screenY,
          depth: z,
          size: Math.max(3, 8 * perspective),
        }
      })

      // Sort by depth for proper rendering
      projectedPoints.sort((a, b) => b.depth - a.depth)

      // Draw connections between nearby points (optional)
      ctx.strokeStyle = "rgba(100, 100, 100, 0.1)"
      ctx.lineWidth = 1

      // Draw points
      projectedPoints.forEach((point) => {
        const color = clusterColors[point.cluster % clusterColors.length]

        // Draw point
        ctx.fillStyle = color
        ctx.beginPath()
        ctx.arc(point.screenX, point.screenY, point.size, 0, 2 * Math.PI)
        ctx.fill()

        // Draw border
        ctx.strokeStyle = "rgba(255, 255, 255, 0.8)"
        ctx.lineWidth = 1
        ctx.stroke()

        // Draw label if enabled
        if (showLabels && point.size > 4) {
          ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
          ctx.font = "12px sans-serif"
          const textWidth = ctx.measureText(point.label).width
          const labelX = point.screenX - textWidth / 2
          const labelY = point.screenY - point.size - 5

          // Background
          ctx.fillRect(labelX - 4, labelY - 12, textWidth + 8, 16)

          // Text
          ctx.fillStyle = "white"
          ctx.fillText(point.label, labelX, labelY)
        }
      })

      animationRef.current = requestAnimationFrame(render)
    }

    render()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [points3D, camera, showLabels, autoRotate])

  const handleMouseDown = (e: React.MouseEvent) => {
    const startX = e.clientX
    const startY = e.clientY
    const startRotationX = camera.rotationX
    const startRotationY = camera.rotationY

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY

      setCamera((prev) => ({
        ...prev,
        rotationY: startRotationY + deltaX * 0.01,
        rotationX: startRotationX + deltaY * 0.01,
      }))
    }

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }

    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)
  }

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault()
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
    setCamera((prev) => ({
      ...prev,
      zoom: Math.max(0.1, Math.min(5, prev.zoom * zoomFactor)),
    }))
  }

  const resetCamera = () => {
    setCamera({
      x: 0,
      y: 0,
      z: 5,
      rotationX: 0,
      rotationY: 0,
      zoom: 1,
    })
  }

  if (embeddings.length < 2) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>3D Embedding Visualization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-muted-foreground">
            <div className="mb-4">Generate embeddings for at least 2 texts to see 3D visualization</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>3D Embedding Visualization</CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary">{points3D.length} points</Badge>
              <Badge variant="outline">{reductionMethod.toUpperCase()}</Badge>
              {pcaResult && (
                <Badge variant="outline">
                  {(pcaResult.explainedVariance.slice(0, 3).reduce((a, b) => a + b, 0) * 100).toFixed(1)}% variance
                </Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={resetCamera}>
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
          <div className="space-y-2">
            <Label>Reduction Method</Label>
            <Select value={reductionMethod} onValueChange={(value: "pca" | "tsne") => setReductionMethod(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pca">PCA (Principal Component Analysis)</SelectItem>
                <SelectItem value="tsne">t-SNE (t-Distributed Stochastic Neighbor Embedding)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Number of Clusters: {numClusters[0]}</Label>
            <Slider
              value={numClusters}
              onValueChange={setNumClusters}
              min={2}
              max={Math.min(8, Math.floor(embeddings.length / 2))}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Switch id="labels" checked={showLabels} onCheckedChange={setShowLabels} />
              <Label htmlFor="labels">Show Labels</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="rotate" checked={autoRotate} onCheckedChange={setAutoRotate} />
              <Label htmlFor="rotate">Auto Rotate</Label>
            </div>
          </div>
        </div>

        {/* 3D Canvas */}
        <div className="relative">
          {isProcessing && (
            <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-10 rounded-lg">
              <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Processing {reductionMethod.toUpperCase()}...</span>
              </div>
            </div>
          )}

          <canvas
            ref={canvasRef}
            className="w-full h-96 border rounded-lg cursor-grab active:cursor-grabbing bg-card"
            onMouseDown={handleMouseDown}
            onWheel={handleWheel}
          />

          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
            Drag to rotate • Scroll to zoom
          </div>
        </div>

        {/* Legend */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {Array.from(new Set(points3D.map((p) => p.cluster))).map((cluster) => (
            <div key={cluster} className="flex items-center gap-2 text-sm">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: clusterColors[cluster % clusterColors.length] }}
              />
              <span>Cluster {cluster + 1}</span>
            </div>
          ))}
        </div>

        {pcaResult && (
          <div className="text-xs text-muted-foreground space-y-1">
            <div>Principal Components Explained Variance:</div>
            {pcaResult.explainedVariance.slice(0, 3).map((variance, i) => (
              <div key={i} className="flex justify-between">
                <span>PC{i + 1}:</span>
                <span>{(variance * 100).toFixed(2)}%</span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
