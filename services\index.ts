// Services Export Index

export { APIClient, apiClient, APIError, isAPIError, getErrorMessage, getErrorInfo } from './api-client'
export { CacheService, createCache } from './cache-service'
export { EmbeddingService, embeddingService } from './embedding-service'
export { VisualizationService, visualizationService } from './visualization-service'
export { SearchService, searchService } from './search-service'
export { ExportService, exportService } from './export-service'

// Re-export types for convenience
export type {
  EmbeddingResult,
  ProcessedText,
  SimilarityResult,
  BatchEmbeddingResponse,
  Point3D,
  PCAResult,
  TSNEResult,
  ClusterResult,
  SearchResult,
  SearchQuery,
  ExportData,
  VisualizationSettings,
  CameraState,
  APIStatus,
  ProcessingProgress,
  ErrorInfo,
} from '@/types'
