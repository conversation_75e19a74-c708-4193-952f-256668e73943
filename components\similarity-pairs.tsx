"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowUpDown, Search, ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"

interface SimilarityPair {
  index1: number
  index2: number
  similarity: number
  label1: string
  label2: string
}

interface SimilarityPairsProps {
  matrix: number[][]
  labels: string[]
  onPairClick?: (pair: SimilarityPair) => void
}

export function SimilarityPairs({ matrix, labels, onPairClick }: SimilarityPairsProps) {
  const [sortOrder, setSortOrder] = useState<"desc" | "asc">("desc")
  const [searchTerm, setSearchTerm] = useState("")

  if (matrix.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">No similarity data available</p>
        </CardContent>
      </Card>
    )
  }

  // Generate all pairs (excluding self-similarity)
  const pairs: SimilarityPair[] = []
  for (let i = 0; i < matrix.length; i++) {
    for (let j = i + 1; j < matrix.length; j++) {
      pairs.push({
        index1: i,
        index2: j,
        similarity: matrix[i][j],
        label1: labels[i],
        label2: labels[j],
      })
    }
  }

  // Filter and sort pairs
  const filteredPairs = pairs
    .filter(
      (pair) =>
        pair.label1.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pair.label2.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .sort((a, b) => (sortOrder === "desc" ? b.similarity - a.similarity : a.similarity - b.similarity))

  const getSimilarityBadgeVariant = (similarity: number) => {
    if (similarity >= 0.7) return "default"
    if (similarity >= 0.4) return "secondary"
    return "outline"
  }

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.7) return "text-chart-1"
    if (similarity >= 0.4) return "text-chart-3"
    return "text-chart-5"
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Text Similarity Pairs</CardTitle>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search pairs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 w-48"
              />
            </div>
            <Button variant="outline" size="sm" onClick={() => setSortOrder(sortOrder === "desc" ? "asc" : "desc")}>
              <ArrowUpDown className="h-4 w-4 mr-2" />
              {sortOrder === "desc" ? "High to Low" : "Low to High"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredPairs.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">No pairs match your search</p>
          ) : (
            filteredPairs.map((pair, index) => (
              <div
                key={`${pair.index1}-${pair.index2}`}
                className={cn(
                  "flex items-center justify-between p-3 border rounded-lg transition-colors",
                  onPairClick ? "cursor-pointer hover:bg-muted/50" : "",
                )}
                onClick={() => onPairClick?.(pair)}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm truncate">{pair.label1}</span>
                    <span className="text-muted-foreground">↔</span>
                    <span className="font-medium text-sm truncate">{pair.label2}</span>
                    {onPairClick && <ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />}
                  </div>
                  <div className="text-xs text-muted-foreground">Pair #{index + 1} • Cosine similarity</div>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <span className={cn("text-lg font-mono font-bold", getSimilarityColor(pair.similarity))}>
                    {(pair.similarity * 100).toFixed(1)}%
                  </span>
                  <Badge variant={getSimilarityBadgeVariant(pair.similarity)}>
                    {pair.similarity >= 0.7 ? "High" : pair.similarity >= 0.4 ? "Medium" : "Low"}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </div>
        {filteredPairs.length > 0 && (
          <div className="mt-4 pt-3 border-t text-xs text-muted-foreground text-center">
            Showing {filteredPairs.length} of {pairs.length} similarity pairs
          </div>
        )}
      </CardContent>
    </Card>
  )
}
