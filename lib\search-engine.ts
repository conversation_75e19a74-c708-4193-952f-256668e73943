import { generateEmbedding, find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, type Embedding<PERSON><PERSON>ult } from "./embedding"

export interface SearchResult {
  id: string
  text: string
  label: string
  similarity: number
  embedding: EmbeddingResult
  rank: number
  highlights?: string[]
}

export interface SearchQuery {
  id: string
  query: string
  timestamp: Date
  results: SearchResult[]
  processingTime: number
}

export interface SearchEngineState {
  corpus: EmbeddingResult[]
  labels: string[]
  ids: string[]
  searchHistory: SearchQuery[]
}

export class SemanticSearchEngine {
  private corpus: EmbeddingResult[] = []
  private labels: string[] = []
  private ids: string[] = []
  private searchHistory: SearchQuery[] = []

  constructor() {}

  // Add documents to the search corpus
  async addDocuments(documents: Array<{ id: string; text: string; label: string }>) {
    for (const doc of documents) {
      const embedding = await generateEmbedding(doc.text)
      this.corpus.push(embedding)
      this.labels.push(doc.label)
      this.ids.push(doc.id)
    }
  }

  // Update the entire corpus
  updateCorpus(embeddings: EmbeddingResult[], labels: string[], ids: string[]) {
    this.corpus = [...embeddings]
    this.labels = [...labels]
    this.ids = [...ids]
  }

  // Perform semantic search
  async search(
    query: string,
    options: {
      topK?: number
      threshold?: number
      includeHighlights?: boolean
    } = {},
  ): Promise<SearchQuery> {
    const startTime = Date.now()
    const { topK = 10, threshold = 0.0, includeHighlights = true } = options

    if (this.corpus.length === 0) {
      return {
        id: Date.now().toString(),
        query,
        timestamp: new Date(),
        results: [],
        processingTime: Date.now() - startTime,
      }
    }

    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query)

    // Find most similar documents
    const similarResults = findMostSimilar(queryEmbedding, this.corpus, Math.min(topK, this.corpus.length))

    // Filter by threshold and create search results
    const results: SearchResult[] = similarResults
      .filter((result) => result.similarity >= threshold)
      .map((result, index) => {
        const corpusIndex = this.corpus.indexOf(result.result)

        return {
          id: this.ids[corpusIndex],
          text: result.result.text,
          label: this.labels[corpusIndex],
          similarity: result.similarity,
          embedding: result.result,
          rank: index + 1,
          highlights: includeHighlights ? this.extractHighlights(query, result.result.text) : undefined,
        }
      })

    const searchQuery: SearchQuery = {
      id: Date.now().toString(),
      query,
      timestamp: new Date(),
      results,
      processingTime: Date.now() - startTime,
    }

    // Add to search history
    this.searchHistory.unshift(searchQuery)
    if (this.searchHistory.length > 50) {
      this.searchHistory = this.searchHistory.slice(0, 50)
    }

    return searchQuery
  }

  // Extract text highlights (simple keyword matching)
  private extractHighlights(query: string, text: string, maxHighlights = 3): string[] {
    const queryWords = query
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 2)
    const sentences = text.split(/[.!?]+/).filter((s) => s.trim().length > 0)
    const highlights: Array<{ sentence: string; score: number }> = []

    for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase()
      let score = 0

      for (const word of queryWords) {
        if (lowerSentence.includes(word)) {
          score += 1
        }
      }

      if (score > 0) {
        highlights.push({ sentence: sentence.trim(), score })
      }
    }

    return highlights
      .sort((a, b) => b.score - a.score)
      .slice(0, maxHighlights)
      .map((h) => h.sentence)
  }

  // Get recommendations based on a document
  async getRecommendations(documentId: string, topK = 5): Promise<SearchResult[]> {
    const docIndex = this.ids.indexOf(documentId)
    if (docIndex === -1 || this.corpus.length === 0) {
      return []
    }

    const sourceEmbedding = this.corpus[docIndex]
    const otherEmbeddings = this.corpus.filter((_, index) => index !== docIndex)
    const otherLabels = this.labels.filter((_, index) => index !== docIndex)
    const otherIds = this.ids.filter((_, index) => index !== docIndex)

    if (otherEmbeddings.length === 0) {
      return []
    }

    const similarResults = findMostSimilar(sourceEmbedding, otherEmbeddings, Math.min(topK, otherEmbeddings.length))

    return similarResults.map((result, index) => {
      const originalIndex = this.corpus.indexOf(result.result)

      return {
        id: this.ids[originalIndex],
        text: result.result.text,
        label: this.labels[originalIndex],
        similarity: result.similarity,
        embedding: result.result,
        rank: index + 1,
      }
    })
  }

  // Get search history
  getSearchHistory(): SearchQuery[] {
    return [...this.searchHistory]
  }

  // Clear search history
  clearSearchHistory() {
    this.searchHistory = []
  }

  // Get corpus statistics
  getCorpusStats() {
    return {
      documentCount: this.corpus.length,
      averageLength:
        this.corpus.length > 0 ? this.corpus.reduce((sum, emb) => sum + emb.text.length, 0) / this.corpus.length : 0,
      totalSearches: this.searchHistory.length,
    }
  }

  // Export corpus for backup/sharing
  exportCorpus() {
    return {
      corpus: this.corpus,
      labels: this.labels,
      ids: this.ids,
      timestamp: new Date().toISOString(),
    }
  }
}
