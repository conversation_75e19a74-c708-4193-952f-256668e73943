// Professional API Client with <PERSON>rror Handling and Retry Logic

import { API_ENDPOINTS, PROCESSING_CONFIG, ERROR_MESSAGES } from '@/constants'
import type { 
  EmbeddingR<PERSON>ult, 
  BatchEmbeddingResponse, 
  APIStatus,
  ErrorInfo 
} from '@/types'

export class APIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class APIClient {
  private baseUrl: string
  private timeout: number
  private maxRetries: number
  private retryDelay: number

  constructor(baseUrl = '', timeout = 30000) {
    this.baseUrl = baseUrl
    this.timeout = timeout
    this.maxRetries = PROCESSING_CONFIG.retryAttempts
    this.retryDelay = PROCESSING_CONFIG.retryDelay
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new APIError(
          errorData.message || ERROR_MESSAGES.serverError,
          response.status,
          errorData.code,
          errorData.details
        )
      }

      return await response.json()
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof APIError) {
        throw error
      }

      if (error.name === 'AbortError') {
        throw new APIError(ERROR_MESSAGES.timeout, 408, 'TIMEOUT')
      }

      if (retryCount < this.maxRetries) {
        await this.delay(this.retryDelay * Math.pow(2, retryCount))
        return this.makeRequest<T>(endpoint, options, retryCount + 1)
      }

      throw new APIError(
        ERROR_MESSAGES.network,
        0,
        'NETWORK_ERROR',
        error.message
      )
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    if (!text || typeof text !== 'string') {
      throw new APIError(ERROR_MESSAGES.invalidInput, 400, 'INVALID_INPUT')
    }

    return this.makeRequest<EmbeddingResult>(API_ENDPOINTS.embeddings, {
      method: 'POST',
      body: JSON.stringify({ text }),
    })
  }

  async generateBatchEmbeddings(texts: string[]): Promise<BatchEmbeddingResponse> {
    if (!Array.isArray(texts) || texts.length === 0) {
      throw new APIError(ERROR_MESSAGES.invalidInput, 400, 'INVALID_INPUT')
    }

    return this.makeRequest<BatchEmbeddingResponse>(API_ENDPOINTS.batch, {
      method: 'POST',
      body: JSON.stringify({ texts }),
    })
  }

  async getStatus(): Promise<APIStatus> {
    return this.makeRequest<APIStatus>(API_ENDPOINTS.status, {
      method: 'GET',
    })
  }

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.makeRequest<{ status: string; timestamp: string }>(
      API_ENDPOINTS.health,
      { method: 'GET' }
    )
  }
}

// Singleton instance
export const apiClient = new APIClient()

// Utility functions for error handling
export function isAPIError(error: unknown): error is APIError {
  return error instanceof APIError
}

export function getErrorMessage(error: unknown): string {
  if (isAPIError(error)) {
    return error.message
  }
  if (error instanceof Error) {
    return error.message
  }
  return 'An unknown error occurred'
}

export function getErrorInfo(error: unknown): ErrorInfo {
  const timestamp = new Date()
  
  if (isAPIError(error)) {
    return {
      code: error.code || 'UNKNOWN',
      message: error.message,
      details: error.details,
      timestamp,
    }
  }
  
  if (error instanceof Error) {
    return {
      code: 'GENERIC_ERROR',
      message: error.message,
      timestamp,
    }
  }
  
  return {
    code: 'UNKNOWN_ERROR',
    message: 'An unknown error occurred',
    timestamp,
  }
}

// Request interceptors for logging and monitoring
export function enableRequestLogging() {
  if (typeof window === 'undefined') return // Skip on server side

  const originalFetch = window.fetch

  window.fetch = async (input, init) => {
    const start = Date.now()
    console.log(`[API] ${init?.method || 'GET'} ${input}`)

    try {
      const response = await originalFetch(input, init)
      const duration = Date.now() - start
      console.log(`[API] ${response.status} ${input} (${duration}ms)`)
      return response
    } catch (error) {
      const duration = Date.now() - start
      console.error(`[API] Error ${input} (${duration}ms):`, error)
      throw error
    }
  }
}

// Enable logging in development (client-side only)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  enableRequestLogging()
}
