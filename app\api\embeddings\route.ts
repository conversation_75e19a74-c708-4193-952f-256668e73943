import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json()

    if (!text || typeof text !== "string") {
      return NextResponse.json({ error: "Text is required" }, { status: 400 })
    }

    const startTime = Date.now()

    // Generate high-quality mock embedding that maintains mathematical properties
    const mockEmbedding = Array.from({ length: 384 }, () => Math.random() * 2 - 1)
    const magnitude = Math.sqrt(mockEmbedding.reduce((sum, val) => sum + val * val, 0))
    const normalizedEmbedding = mockEmbedding.map((val) => val / magnitude)

    const processingTime = Date.now() - startTime

    return NextResponse.json({
      text,
      embedding: normalizedEmbedding,
      processingTime,
      dimension: 384,
      model: "Mock BGE-small-en-v1.5 (Demo Mode)",
      note: "Using mock embeddings - Python ML environment required for real BGE embeddings",
      fallback: true,
      requirements: {
        python: "Python 3.8+",
        packages: ["transformers", "torch", "scikit-learn", "numpy"],
        install: "pip install transformers torch scikit-learn numpy",
      },
    })
  } catch (error) {
    console.error("Embedding generation error:", error)
    return NextResponse.json(
      {
        error: "Failed to generate embedding",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}

export async function GET() {
  return NextResponse.json({
    status: "API is working",
    timestamp: new Date().toISOString(),
    note: "Embedding API is available in demo mode",
    mode: "mock_embeddings",
    requirements: {
      for_real_embeddings: {
        python: "Python 3.8+",
        packages: ["transformers", "torch", "scikit-learn", "numpy"],
        install: "pip install transformers torch scikit-learn numpy",
      },
    },
  })
}
