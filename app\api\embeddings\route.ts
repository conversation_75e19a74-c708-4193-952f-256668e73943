import { type NextRequest, NextResponse } from "next/server"
import { spawn } from "child_process"
import { EMBEDDING_CONFIG, PYTHON_CONFIG, VALIDATION_RULES } from "@/constants"
import type { EmbeddingResult } from "@/types"

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json()

    // Validate input
    if (!text || typeof text !== "string") {
      return NextResponse.json({ error: "Text is required" }, { status: 400 })
    }

    if (text.length > VALIDATION_RULES.text.maxLength) {
      return NextResponse.json(
        { error: `Text exceeds maximum length of ${VALIDATION_RULES.text.maxLength} characters` },
        { status: 400 }
      )
    }

    const startTime = Date.now()

    try {
      // Try to use Python service first
      const result = await generateRealEmbedding(text)
      return NextResponse.json(result)
    } catch (pythonError) {
      console.warn("Python service unavailable, falling back to mock embeddings:", pythonError)

      // Fallback to mock embeddings
      const mockResult = generateMockEmbedding(text, Date.now() - startTime)
      return NextResponse.json(mockResult)
    }
  } catch (error) {
    console.error("Embedding generation error:", error)
    return NextResponse.json(
      {
        error: "Failed to generate embedding",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

async function generateRealEmbedding(text: string): Promise<EmbeddingResult> {
  return new Promise((resolve, reject) => {
    const python = spawn("python", [PYTHON_CONFIG.scriptPath, "single", text], {
      encoding: PYTHON_CONFIG.encoding as BufferEncoding,
    })

    let output = ""
    let errorOutput = ""

    python.stdout.on("data", (data) => {
      output += data.toString()
    })

    python.stderr.on("data", (data) => {
      errorOutput += data.toString()
    })

    python.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Python process exited with code ${code}: ${errorOutput}`))
        return
      }

      try {
        const result = JSON.parse(output.trim())
        resolve({
          text: result.text,
          embedding: result.embedding,
          processingTime: result.processing_time || 0,
          dimension: result.dimension,
          model: result.model,
        })
      } catch (parseError) {
        reject(new Error(`Failed to parse Python output: ${parseError}`))
      }
    })

    python.on("error", (error) => {
      reject(new Error(`Failed to spawn Python process: ${error.message}`))
    })

    // Set timeout
    setTimeout(() => {
      python.kill()
      reject(new Error("Python process timeout"))
    }, PYTHON_CONFIG.timeout)
  })
}

function generateMockEmbedding(text: string, processingTime: number): EmbeddingResult {
  // Generate high-quality mock embedding that maintains mathematical properties
  const mockEmbedding = Array.from({ length: EMBEDDING_CONFIG.models['bge-small-en-v1.5'].dimension }, () =>
    Math.random() * 2 - 1
  )
  const magnitude = Math.sqrt(mockEmbedding.reduce((sum, val) => sum + val * val, 0))
  const normalizedEmbedding = mockEmbedding.map((val) => val / magnitude)

  return {
    text,
    embedding: normalizedEmbedding,
    processingTime,
    dimension: EMBEDDING_CONFIG.models['bge-small-en-v1.5'].dimension,
    model: "Mock BGE-small-en-v1.5 (Demo Mode)",
    note: "Using mock embeddings - Python ML environment required for real BGE embeddings",
    fallback: true,
    requirements: {
      python: "Python 3.8+",
      packages: PYTHON_CONFIG.requirements,
      install: PYTHON_CONFIG.installCommand,
    },
  }
}

export async function GET() {
  // Check if Python service is available
  let pythonAvailable = false
  try {
    await generateRealEmbedding("test")
    pythonAvailable = true
  } catch {
    pythonAvailable = false
  }

  return NextResponse.json({
    status: "API is working",
    timestamp: new Date().toISOString(),
    mode: pythonAvailable ? "real_embeddings" : "mock_embeddings",
    pythonAvailable,
    model: EMBEDDING_CONFIG.defaultModel,
    supportedModels: Object.keys(EMBEDDING_CONFIG.models),
    requirements: pythonAvailable ? undefined : {
      python: "Python 3.8+",
      packages: PYTHON_CONFIG.requirements,
      install: PYTHON_CONFIG.installCommand,
    },
  })
}
