import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Info, Terminal } from "lucide-react"

interface PythonRequirementsNoticeProps {
  show: boolean
}

export function PythonRequirementsNotice({ show }: PythonRequirementsNoticeProps) {
  if (!show) return null

  return (
    <Alert className="mb-6 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
      <Info className="h-4 w-4 text-amber-600 dark:text-amber-400" />
      <AlertTitle className="text-amber-800 dark:text-amber-200">Demo Mode Active</AlertTitle>
      <AlertDescription className="text-amber-700 dark:text-amber-300">
        <p className="mb-3">
          Currently using mock embeddings for demonstration. For real BGE embeddings, you need a Python environment with
          ML libraries.
        </p>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Requirements
            </Badge>
            <span className="text-sm">Python 3.8+</span>
          </div>
          <div className="flex items-center gap-2 font-mono text-sm bg-amber-100 dark:bg-amber-900 p-2 rounded">
            <Terminal className="h-3 w-3" />
            <code>pip install transformers torch scikit-learn numpy</code>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}
