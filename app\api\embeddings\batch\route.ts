import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { texts } = await request.json()

    if (!texts || !Array.isArray(texts)) {
      return NextResponse.json({ error: "Texts array is required" }, { status: 400 })
    }

    const startTime = Date.now()

    // Generate mock embeddings for each text
    const embeddings = texts.map(() => {
      const mockEmbedding = Array.from({ length: 384 }, () => Math.random() * 2 - 1)
      const magnitude = Math.sqrt(mockEmbedding.reduce((sum, val) => sum + val * val, 0))
      return mockEmbedding.map((val) => val / magnitude)
    })

    // Calculate similarity matrix
    const similarity_matrix = embeddings.map((emb1) =>
      embeddings.map((emb2) => {
        const dotProduct = emb1.reduce((sum, val, i) => sum + val * emb2[i], 0)
        return Math.max(0, Math.min(1, dotProduct)) // Clamp between 0 and 1
      }),
    )

    // Simple PCA-like dimensionality reduction for 3D visualization
    const visualization_3d = {
      coordinates: embeddings.map(() => [Math.random() * 10 - 5, Math.random() * 10 - 5, Math.random() * 10 - 5]),
      variance_explained: [0.4, 0.3, 0.2],
      method: "Mock PCA",
    }

    const processingTime = Date.now() - startTime

    return NextResponse.json({
      texts,
      embeddings,
      similarity_matrix,
      visualization_3d,
      processingTime,
      totalTexts: texts.length,
      dimension: 384,
      model: "Mock BGE-small-en-v1.5 (Demo Mode)",
      note: "Using mock embeddings - Python ML environment required for real BGE embeddings",
      fallback: true,
      requirements: {
        python: "Python 3.8+",
        packages: ["transformers", "torch", "scikit-learn", "numpy"],
        install: "pip install transformers torch scikit-learn numpy",
      },
    })
  } catch (error) {
    console.error("Batch embedding generation error:", error)
    return NextResponse.json({ error: "Failed to generate batch embeddings" }, { status: 500 })
  }
}
