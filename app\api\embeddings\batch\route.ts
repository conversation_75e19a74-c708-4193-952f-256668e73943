import { type NextRequest, NextResponse } from "next/server"
import { spawn } from "child_process"
import { EMBEDDING_CONFIG, PYTHON_CONFIG, VALIDATION_RULES } from "@/constants"
import type { BatchEmbeddingResponse } from "@/types"

export async function POST(request: NextRequest) {
  try {
    const { texts } = await request.json()

    // Validate input
    if (!texts || !Array.isArray(texts)) {
      return NextResponse.json({ error: "Texts array is required" }, { status: 400 })
    }

    if (texts.length > VALIDATION_RULES.batch.maxSize) {
      return NextResponse.json(
        { error: `Batch size exceeds maximum of ${VALIDATION_RULES.batch.maxSize}` },
        { status: 400 }
      )
    }

    // Validate each text
    for (let i = 0; i < texts.length; i++) {
      const text = texts[i]
      if (!text || typeof text !== "string") {
        return NextResponse.json(
          { error: `Invalid text at index ${i}` },
          { status: 400 }
        )
      }
      if (text.length > VALIDATION_RULES.text.maxLength) {
        return NextResponse.json(
          { error: `Text at index ${i} exceeds maximum length of ${VALIDATION_RULES.text.maxLength} characters` },
          { status: 400 }
        )
      }
    }

    const startTime = Date.now()

    try {
      // Try to use Python service first
      const result = await generateRealBatchEmbeddings(texts)
      return NextResponse.json(result)
    } catch (pythonError) {
      console.warn("Python service unavailable, falling back to mock embeddings:", pythonError)

      // Fallback to mock embeddings
      const mockResult = generateMockBatchEmbeddings(texts, Date.now() - startTime)
      return NextResponse.json(mockResult)
    }
  } catch (error) {
    console.error("Batch embedding generation error:", error)
    return NextResponse.json(
      {
        error: "Failed to generate batch embeddings",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

async function generateRealBatchEmbeddings(texts: string[]): Promise<BatchEmbeddingResponse> {
  return new Promise((resolve, reject) => {
    const python = spawn("python", [PYTHON_CONFIG.scriptPath, "batch", JSON.stringify(texts)], {
      encoding: PYTHON_CONFIG.encoding as BufferEncoding,
    })

    let output = ""
    let errorOutput = ""

    python.stdout.on("data", (data) => {
      output += data.toString()
    })

    python.stderr.on("data", (data) => {
      errorOutput += data.toString()
    })

    python.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Python process exited with code ${code}: ${errorOutput}`))
        return
      }

      try {
        const result = JSON.parse(output.trim())
        resolve({
          texts: result.texts,
          embeddings: result.embeddings,
          similarity_matrix: result.similarity_matrix,
          visualization_3d: result.visualization_3d,
          processingTime: result.processing_time || 0,
          totalTexts: result.count,
          dimension: result.dimension,
          model: result.model,
        })
      } catch (parseError) {
        reject(new Error(`Failed to parse Python output: ${parseError}`))
      }
    })

    python.on("error", (error) => {
      reject(new Error(`Failed to spawn Python process: ${error.message}`))
    })

    // Set timeout
    setTimeout(() => {
      python.kill()
      reject(new Error("Python process timeout"))
    }, PYTHON_CONFIG.timeout)
  })
}

function generateMockBatchEmbeddings(texts: string[], processingTime: number): BatchEmbeddingResponse {
  const dimension = EMBEDDING_CONFIG.models['bge-small-en-v1.5'].dimension

  // Generate mock embeddings for each text
  const embeddings = texts.map(() => {
    const mockEmbedding = Array.from({ length: dimension }, () => Math.random() * 2 - 1)
    const magnitude = Math.sqrt(mockEmbedding.reduce((sum, val) => sum + val * val, 0))
    return mockEmbedding.map((val) => val / magnitude)
  })

  // Calculate similarity matrix
  const similarity_matrix = embeddings.map((emb1) =>
    embeddings.map((emb2) => {
      const dotProduct = emb1.reduce((sum, val, i) => sum + val * emb2[i], 0)
      return Math.max(0, Math.min(1, dotProduct)) // Clamp between 0 and 1
    })
  )

  // Simple PCA-like dimensionality reduction for 3D visualization
  const visualization_3d = {
    coordinates: embeddings.map(() => [
      Math.random() * 10 - 5,
      Math.random() * 10 - 5,
      Math.random() * 10 - 5
    ]),
    variance_explained: [0.4, 0.3, 0.2],
    method: "Mock PCA",
  }

  return {
    texts,
    embeddings,
    similarity_matrix,
    visualization_3d,
    processingTime,
    totalTexts: texts.length,
    dimension,
    model: "Mock BGE-small-en-v1.5 (Demo Mode)",
    note: "Using mock embeddings - Python ML environment required for real BGE embeddings",
    fallback: true,
    requirements: {
      python: "Python 3.8+",
      packages: PYTHON_CONFIG.requirements,
      install: PYTHON_CONFIG.installCommand,
    },
  }
}
