import { NextResponse } from "next/server"
import { exec } from "child_process"
import { promisify } from "util"

const execAsync = promisify(exec)

export async function GET() {
  try {
    // Test basic Python execution
    try {
      const { stdout: pythonVersion } = await execAsync("python --version")
      console.log("Python version:", pythonVersion)
    } catch (pythonError) {
      return NextResponse.json({
        python_available: false,
        error: "Python not found in environment",
        status: "failed",
        requirements: [
          "Python 3.8+",
          "pip install transformers torch scikit-learn numpy",
          "For production: Use a Python-enabled deployment environment",
        ],
      })
    }

    // Test if required packages are available
    try {
      const { stdout: packageTest } = await execAsync(
        "python -c \"import transformers, torch, sklearn, numpy; print('All packages available')\"",
      )
      console.log("Package test:", packageTest)
    } catch (packageError) {
      return NextResponse.json({
        python_available: true,
        packages_available: false,
        error: "Required Python packages not installed",
        status: "failed",
        requirements: ["pip install transformers torch scikit-learn numpy", "Or use: pip install -r requirements.txt"],
      })
    }

    // Test if our script exists and can run
    try {
      const { stdout: testResult } = await execAsync("python scripts/embedding_service.py test")
      const result = JSON.parse(testResult)

      return NextResponse.json({
        python_available: true,
        packages_available: true,
        script_test: result,
        status: "success",
      })
    } catch (scriptError) {
      return NextResponse.json({
        python_available: true,
        packages_available: true,
        script_available: false,
        error: "Embedding script failed to execute",
        status: "failed",
        script_error: scriptError instanceof Error ? scriptError.message : "Unknown script error",
      })
    }
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : "Unknown error",
      status: "failed",
    })
  }
}
