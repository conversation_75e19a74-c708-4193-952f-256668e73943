# Next.js BGE Text Embedding Visualization App

A comprehensive web application that demonstrates the capabilities of the BAAI/bge-small-en-v1.5 text embedding model through interactive visualizations, semantic similarity analysis, and 3D embedding space exploration.

##  Features

### Core Functionality
- **Real-time Text Embedding Generation** - Process multiple texts simultaneously using BGE embeddings
- **Interactive Similarity Analysis** - Color-coded heatmaps and detailed similarity matrices
- **3D Visualization** - Explore embedding spaces using PCA dimensionality reduction
- **Semantic Search Engine** - Find similar texts and get recommendations
- **Multi-format Export** - Export embeddings and analysis results in JSON/CSV formats

### User Interface
- **Drag & Drop File Upload** - Support for TXT, PDF, and DOCX files
- **Multi-text Input Interface** - Add, edit, and manage multiple text inputs
- **Real-time Processing** - Live progress indicators and performance metrics
- **Responsive Design** - Works seamlessly on desktop and mobile devices
- **Dark/Light Mode** - Toggle between themes with system preference detection

### Advanced Features
- **Batch Processing** - Handle multiple documents efficiently
- **Caching System** - Store previously processed embeddings for faster access
- **Search History** - Track and revisit previous searches
- **Interactive Tooltips** - Detailed information on hover and click
- **Performance Analytics** - Processing time and model performance metrics

##  Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Quick Start

1. **Clone or Download the Project**
   \`\`\`bash
   # If using GitHub
   git clone <repository-url>
   cd nextjs-bge-app
   
   # Or download and extract the ZIP file
   \`\`\`

2. **Install Dependencies**
   \`\`\`bash
   npm install
   # or
   yarn install
   \`\`\`

3. **Run the Development Server**
   \`\`\`bash
   npm run dev
   # or
   yarn dev
   \`\`\`

4. **Open in Browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

##  Python Backend (Optional - For Real Embeddings)

The app works with mock embeddings by default. For real BGE embeddings, set up the Python backend:

### Python Requirements
- Python 3.8 or higher
- Required packages: `transformers`, `torch`, `scikit-learn`, `numpy`

### Installation
\`\`\`bash
# Install Python dependencies
pip install transformers torch scikit-learn numpy

# For GPU support (optional)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
\`\`\`

### Verification
The app will automatically detect if Python dependencies are available and switch to real embeddings.

##  Project Structure

\`\`\`
├── app/
│   ├── api/
│   │   ├── embeddings/          # Embedding generation endpoints
│   │   └── test-python/         # Python backend testing
│   ├── globals.css              # Global styles and design tokens
│   ├── layout.tsx               # Root layout with theme provider
│   └── page.tsx                 # Main application interface
├── components/
│   ├── ui/                      # Reusable UI components (shadcn/ui)
│   ├── embedding-3d-visualization.tsx  # 3D scatter plot component
│   ├── export-dialog.tsx        # Export functionality
│   ├── file-upload.tsx          # Drag & drop file upload
│   ├── search-interface.tsx     # Semantic search component
│   ├── similarity-matrix.tsx    # Interactive heatmap visualization
│   ├── similarity-stats.tsx     # Statistical analysis display
│   └── text-input-card.tsx      # Individual text input management
├── lib/
│   ├── dimensionality-reduction.ts  # PCA implementation
│   ├── embedding.ts             # Client-side embedding utilities
│   ├── export-utils.ts          # Data export functionality
│   ├── search-engine.ts         # Semantic search logic
│   └── utils.ts                 # Utility functions
├── scripts/
│   └── embedding_service.py     # Python backend for real embeddings
└── README.md
\`\`\`

##  Usage Guide

### 1. Text Input
- **Manual Entry**: Type or paste text directly into input fields
- **File Upload**: Drag and drop TXT, PDF, or DOCX files
- **Batch Processing**: Add multiple texts for simultaneous processing

### 2. Embedding Generation
- Click "Generate Embeddings" to process all texts
- Monitor progress with real-time indicators
- View processing metrics and performance data

### 3. Similarity Analysis
- Navigate to the "Similarity Analysis" tab
- Explore the interactive heatmap
- Click on cells for detailed similarity scores
- Use the search functionality to find specific text pairs

### 4. 3D Visualization
- Switch to the "3D Visualization" tab
- Interact with the 3D scatter plot using mouse controls
- Toggle labels and auto-rotation
- Analyze clustering patterns in embedding space

### 5. Semantic Search
- Go to the "Search" tab
- Enter queries to find semantically similar texts
- Browse recommendations based on embedding similarity
- Review search history and saved queries

### 6. Export Data
- Use the export dialog to download results
- Choose between JSON and CSV formats
- Export embeddings, similarity matrices, or search results

##  Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with default settings
4. For Python backend, ensure your deployment environment supports Python execution

### Other Platforms
- **Netlify**: Works with static export
- **Docker**: Use the included Dockerfile for containerization
- **Traditional Hosting**: Build with `npm run build` and serve the `out` directory

## Configuration

### Environment Variables
\`\`\`env
# Optional: Custom model configurations
EMBEDDING_MODEL_NAME=BAAI/bge-small-en-v1.5
PYTHON_PATH=/usr/bin/python3
\`\`\`

### Customization
- **Colors**: Modify design tokens in `app/globals.css`
- **Models**: Update model name in `scripts/embedding_service.py`
- **Features**: Enable/disable features in component configurations

##  Troubleshooting

### Common Issues

**"Python backend not available"**
- Install required Python packages: `pip install transformers torch scikit-learn numpy`
- Verify Python is accessible from the command line

**"Model loading failed"**
- Check internet connection for model download
- Ensure sufficient disk space for model files
- Try clearing browser cache and restarting

**"File upload not working"**
- Verify file format (TXT, PDF, DOCX only)
- Check file size limits
- Ensure proper file permissions

### Performance Tips
- Use batch processing for multiple documents
- Enable caching for frequently processed texts
- Close unused browser tabs to free memory
- Use GPU acceleration when available (Python backend)

##  Technical Details

### Embedding Model
- **Model**: BAAI/bge-small-en-v1.5
- **Dimensions**: 384
- **Context Length**: 512 tokens
- **Language**: English (optimized)

### Similarity Calculation
- **Method**: Cosine similarity
- **Range**: -1 to 1 (higher = more similar)
- **Normalization**: L2 normalized embeddings

### 3D Visualization
- **Reduction**: Principal Component Analysis (PCA)
- **Components**: 3 (from 384 dimensions)
- **Rendering**: HTML5 Canvas with interactive controls



##  Acknowledgments

- **BAAI** for the BGE embedding model
- **Hugging Face** for the transformers library
- **Vercel** for the deployment platform
- **shadcn/ui** for the component library

---

**Need Help?** Open an issue on GitHub or check the troubleshooting section above.
