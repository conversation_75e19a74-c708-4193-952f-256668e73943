// Core Types for BGE Embedding Visualizer

export interface EmbeddingResult {
  text: string
  embedding: number[]
  processingTime: number
  dimension: number
  model: string
  note?: string
  fallback?: boolean
  requirements?: {
    python?: string
    packages?: string[]
    install?: string
  }
}

export interface ProcessedText {
  id: string
  text: string
  label: string
  embedding?: EmbeddingResult
  isProcessing?: boolean
  error?: string
}

export interface SimilarityResult {
  similarity: number
  result: EmbeddingResult
  index: number
}

export interface BatchEmbeddingResponse {
  texts: string[]
  embeddings: number[][]
  similarity_matrix: number[][]
  visualization_3d: {
    coordinates: number[][]
    variance_explained: number[]
    method: string
  }
  processingTime: number
  totalTexts: number
  dimension: number
  model: string
  note?: string
  fallback?: boolean
  requirements?: {
    python?: string
    packages?: string[]
    install?: string
  }
}

export interface Point3D {
  x: number
  y: number
  z: number
  label: string
  color: string
  cluster?: number
  originalIndex: number
}

export interface PCAResult {
  reducedData: number[][]
  explainedVariance: number[]
  totalVariance: number
  components: number[][]
}

export interface TSNEResult {
  reducedData: number[][]
  perplexity: number
  iterations: number
}

export interface ClusterResult {
  labels: number[]
  centroids: number[][]
  inertia: number
  numClusters: number
}

export interface SearchResult {
  id: string
  text: string
  label: string
  similarity: number
  embedding: EmbeddingResult
  rank: number
  highlights?: string[]
}

export interface SearchQuery {
  id: string
  query: string
  timestamp: Date
  results: SearchResult[]
  processingTime: number
}

export interface ExportData {
  texts: string[]
  labels: string[]
  embeddings: number[][]
  similarityMatrix: number[][]
  metadata: {
    model: string
    dimension: number
    timestamp: string
    totalTexts: number
  }
}

export interface VisualizationSettings {
  reductionMethod: 'pca' | 'tsne'
  numClusters: number
  showLabels: boolean
  autoRotate: boolean
  pointSize: number
  colorScheme: 'default' | 'cluster' | 'similarity'
}

export interface CameraState {
  x: number
  y: number
  z: number
  rotationX: number
  rotationY: number
  zoom: number
}

export interface APIStatus {
  status: string
  timestamp: string
  mode: 'real' | 'mock'
  pythonAvailable: boolean
  requirements?: {
    python?: string
    packages?: string[]
    install?: string
  }
}

export interface ProcessingProgress {
  current: number
  total: number
  stage: string
  message: string
}

export interface ErrorInfo {
  code: string
  message: string
  details?: any
  timestamp: Date
}

// Utility Types
export type EmbeddingModel = 'bge-small-en-v1.5' | 'mock'
export type ExportFormat = 'json' | 'csv' | 'xlsx'
export type VisualizationMode = '2d' | '3d'
export type ColorScheme = 'default' | 'cluster' | 'similarity' | 'rainbow'

// Component Props Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface EmbeddingVisualizationProps extends BaseComponentProps {
  embeddings: EmbeddingResult[]
  labels: string[]
  onPointClick?: (index: number) => void
  settings?: Partial<VisualizationSettings>
}

export interface SimilarityMatrixProps extends BaseComponentProps {
  matrix: number[][]
  labels: string[]
  onCellClick?: (row: number, col: number) => void
}

export interface SearchInterfaceProps extends BaseComponentProps {
  embeddings: EmbeddingResult[]
  labels: string[]
  onSearch?: (query: SearchQuery) => void
}

// Configuration Types
export interface AppConfig {
  api: {
    baseUrl: string
    timeout: number
    retries: number
  }
  embedding: {
    defaultModel: EmbeddingModel
    batchSize: number
    cacheEnabled: boolean
  }
  visualization: {
    defaultSettings: VisualizationSettings
    maxPoints: number
    animationDuration: number
  }
  export: {
    maxFileSize: number
    supportedFormats: ExportFormat[]
  }
}

// Event Types
export interface EmbeddingEvent {
  type: 'start' | 'progress' | 'complete' | 'error'
  data?: any
  timestamp: Date
}

export interface VisualizationEvent {
  type: 'render' | 'interaction' | 'settings_change'
  data?: any
  timestamp: Date
}
