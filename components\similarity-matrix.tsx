"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ltip, TooltipContent, <PERSON><PERSON>ipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface SimilarityMatrixProps {
  matrix: number[][]
  labels: string[]
  className?: string
}

export function SimilarityMatrix({ matrix, labels, className }: SimilarityMatrixProps) {
  const [hoveredCell, setHoveredCell] = useState<{ row: number; col: number } | null>(null)

  if (matrix.length === 0 || labels.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">No similarity data available</p>
        </CardContent>
      </Card>
    )
  }

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.9) return "bg-chart-1"
    if (similarity >= 0.7) return "bg-chart-2"
    if (similarity >= 0.5) return "bg-chart-3"
    if (similarity >= 0.3) return "bg-chart-4"
    return "bg-chart-5"
  }

  const getSimilarityIntensity = (similarity: number) => {
    const intensity = Math.round(similarity * 100)
    if (intensity >= 90) return "opacity-100"
    if (intensity >= 70) return "opacity-80"
    if (intensity >= 50) return "opacity-60"
    if (intensity >= 30) return "opacity-40"
    return "opacity-20"
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Similarity Matrix
            <Badge variant="secondary">
              {matrix.length}×{matrix.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-auto">
            <div className="inline-block min-w-full">
              {/* Column headers */}
              <div className="flex mb-2">
                <div className="w-24 flex-shrink-0"></div>
                {labels.map((label, index) => (
                  <div
                    key={index}
                    className="w-16 h-8 flex items-center justify-center text-xs font-medium text-muted-foreground truncate px-1"
                    title={label}
                  >
                    {label.length > 8 ? `${label.substring(0, 8)}...` : label}
                  </div>
                ))}
              </div>

              {/* Matrix rows */}
              {matrix.map((row, rowIndex) => (
                <div key={rowIndex} className="flex mb-1">
                  {/* Row header */}
                  <div
                    className="w-24 h-12 flex items-center text-xs font-medium text-muted-foreground pr-2 truncate"
                    title={labels[rowIndex]}
                  >
                    {labels[rowIndex].length > 12 ? `${labels[rowIndex].substring(0, 12)}...` : labels[rowIndex]}
                  </div>

                  {/* Matrix cells */}
                  {row.map((similarity, colIndex) => (
                    <Tooltip key={colIndex}>
                      <TooltipTrigger asChild>
                        <div
                          className={cn(
                            "w-16 h-12 border border-border/50 flex items-center justify-center text-xs font-mono cursor-pointer transition-all duration-200 hover:scale-105 hover:z-10 relative",
                            getSimilarityColor(similarity),
                            getSimilarityIntensity(similarity),
                            hoveredCell?.row === rowIndex || hoveredCell?.col === colIndex
                              ? "ring-2 ring-primary/50"
                              : "",
                            rowIndex === colIndex ? "ring-1 ring-primary/30" : "",
                          )}
                          onMouseEnter={() => setHoveredCell({ row: rowIndex, col: colIndex })}
                          onMouseLeave={() => setHoveredCell(null)}
                        >
                          <span className="text-foreground font-semibold drop-shadow-sm">{similarity.toFixed(2)}</span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="font-medium">
                            {labels[rowIndex]} ↔ {labels[colIndex]}
                          </p>
                          <p className="text-sm">Similarity: {(similarity * 100).toFixed(1)}%</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Legend */}
          <div className="mt-6 pt-4 border-t">
            <h4 className="text-sm font-medium mb-3">Similarity Scale</h4>
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-chart-1 rounded"></div>
                <span>0.9+ (Very High)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-chart-2 opacity-80 rounded"></div>
                <span>0.7-0.9 (High)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-chart-3 opacity-60 rounded"></div>
                <span>0.5-0.7 (Medium)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-chart-4 opacity-40 rounded"></div>
                <span>0.3-0.5 (Low)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-chart-5 opacity-20 rounded"></div>
                <span>0.0-0.3 (Very Low)</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  )
}
